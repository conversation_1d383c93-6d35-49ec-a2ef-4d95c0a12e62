"""
PDF Converter Module

This module provides functionality to convert PDF files to images using the pdf2image library.
It can process either a single PDF file or a directory containing multiple PDF files.
It can also optionally crop the images based on specified ratios.

The default source directory for sample PDFs is 'data/Parallel_pdf_07042568'.
"""

import os
import random
from typing import List, Dict, Tuple
from pdf2image import convert_from_path
from glob import glob
from tqdm import tqdm
from PIL import Image

# Default directories
DEFAULT_PDF_DIR = "data/Parallel_pdf_07042568"
DEFAULT_OUTPUT_DIR = "data/process_data/for_label"

# Default crop coordinates in format [(x1, y1), (x2, y2)]
# Where (x1, y1) is the top-left corner and (x2, y2) is the bottom-right corner
# Values are relative to image dimensions (0.0 to 1.0)
DEFAULT_CROP_COORDS = [(0.10, 0.77), (0.87, 0.25)]  # Equivalent to previous default crop ratios

# Image format mapping
FORMAT_MAPPING = {
    'jpg': 'JPEG',
    'jpeg': 'JPEG',
    'png': 'PNG',
    'tiff': 'TIFF',
    'bmp': 'BMP',
    'gif': 'GIF'
}

def crop_image(
    image: Image.Image,
    crop_coords: List[Tuple[float, float]]
) -> Image.Image:
    """
    Crop an image based on specified relative coordinates.

    Args:
        image (Image.Image): The image to crop
        crop_coords (List[Tuple[float, float]]): List of two tuples representing the top-left and bottom-right
            corners of the crop box in relative coordinates. Format: [(x1, y1), (x2, y2)]
            where x and y values are between 0.0 and 1.0.

    Returns:
        Image.Image: The cropped image

    Example:
        # Crop the middle 50% of the image
        crop_coords = [(0.25, 0.25), (0.75, 0.75)]
        cropped_image = crop_image(original_image, crop_coords)
    """
    # Validate input
    if len(crop_coords) != 2:
        raise ValueError("crop_coords must contain exactly 2 coordinate tuples [(x1, y1), (x2, y2)]")

    # Get image dimensions
    width, height = image.size

    # Extract coordinates
    (x1_rel, y1_rel), (x2_rel, y2_rel) = crop_coords

    # Convert relative coordinates to absolute pixel values
    x1 = int(x1_rel * width)
    y1 = int(y1_rel * height)
    x2 = int(x2_rel * width)
    y2 = int(y2_rel * height)

    # Ensure coordinates are within image bounds
    x1 = max(0, min(x1, width))
    y1 = max(0, min(y1, height))
    x2 = max(0, min(x2, width))
    y2 = max(0, min(y2, height))

    # Ensure x2 > x1 and y2 > y1
    if x2 <= x1 or y2 <= y1:
        raise ValueError(f"Invalid crop coordinates: {crop_coords}. Ensure x2 > x1 and y2 > y1.")

    # Create crop box (left, upper, right, lower)
    crop_box = (x1, y1, x2, y2)

    # Crop the image
    return image.crop(crop_box)

def ensure_directory_exists(directory: str) -> None:
    """
    Ensure that the specified directory exists, creating it if necessary.

    Args:
        directory (str): Directory path to check/create
    """
    if not os.path.exists(directory):
        os.makedirs(directory, exist_ok=True)
        print(f"Created directory: {directory}")

def get_output_directory_for_pdf(base_output_dir: str, pdf_name: str, create_subdirs: bool = True) -> str:
    """
    Get the output directory for a specific PDF, creating a subdirectory with the PDF name.

    Args:
        base_output_dir (str): Base output directory
        pdf_name (str): Name of the PDF file (without extension)
        create_subdirs (bool, optional): Whether to create subdirectories. Defaults to True.

    Returns:
        str: Path to the output directory for the PDF
    """
    # Create a sanitized version of the PDF name for the directory
    # Remove any characters that might cause issues in directory names
    sanitized_name = ''.join(c if c.isalnum() or c in ['-', '_'] else '_' for c in pdf_name)

    # Create the output directory path
    output_dir = os.path.join(base_output_dir, sanitized_name)

    # Create the directory if it doesn't exist and create_subdirs is True
    if create_subdirs:
        ensure_directory_exists(output_dir)

    return output_dir

def get_sample_pdf(directory: str = DEFAULT_PDF_DIR) -> str:
    """
    Get a random sample PDF file from the specified directory.

    Args:
        directory (str, optional): Directory to get a sample PDF from. Defaults to DEFAULT_PDF_DIR.

    Returns:
        str: Path to a sample PDF file

    Raises:
        FileNotFoundError: If the directory does not exist or contains no PDF files
    """
    if not os.path.exists(directory):
        raise FileNotFoundError(f"The directory '{directory}' does not exist.")

    pdf_files = glob(os.path.join(directory, "*.pdf"))
    if not pdf_files:
        raise FileNotFoundError(f"No PDF files found in '{directory}'.")

    return random.choice(pdf_files)

def convert_pdf_to_images(
    input_path: str = DEFAULT_PDF_DIR,
    output_dir: str = DEFAULT_OUTPUT_DIR,
    dpi: int = 300,
    output_format: str = "jpg",
    save_images: bool = True,
    crop_images: bool = False,
    crop_coords: List[Tuple[float, float]] = None,
    organize_by_pdf: bool = True
) -> Dict[str, List]:
    """
    Convert PDF files to images. Can handle both single PDF files and directories containing PDFs.

    Args:
        input_path (str): Path to a PDF file or directory containing PDF files. Defaults to DEFAULT_PDF_DIR.
        output_dir (str, optional): Directory to save the converted images. Defaults to DEFAULT_OUTPUT_DIR.
        dpi (int, optional): DPI for the output images. Higher values result in larger, higher quality images. Defaults to 300.
        output_format (str, optional): Format to save the images as. Defaults to "jpg".
            Supported formats: 'jpg', 'jpeg', 'png', 'tiff', 'bmp', 'gif'
        save_images (bool, optional): Whether to save the images to disk. Defaults to True.
        crop_images (bool, optional): Whether to crop the images. Defaults to False.
        crop_coords (List[Tuple[float, float]], optional): List of two tuples representing the top-left and bottom-right
            corners of the crop box in relative coordinates. Format: [(x1, y1), (x2, y2)]
            where x and y values are between 0.0 and 1.0. If None, default values will be used.
        organize_by_pdf (bool, optional): Whether to organize output images in subdirectories named after the PDF files. Defaults to True.

    Returns:
        Dict[str, List]: Dictionary mapping PDF filenames to lists of converted images (either full-size or cropped)

    Example:
        # Crop the middle 50% of the image
        crop_coords = [(0.25, 0.25), (0.75, 0.75)]
        convert_pdf_to_images("input.pdf", crop_images=True, crop_coords=crop_coords)
    """
    # Validate output format
    if output_format.lower() not in FORMAT_MAPPING and save_images:
        print(f"Warning: Unsupported output format '{output_format}'. Using 'jpg' instead.")
        output_format = "jpg"
    # Check if input_path exists
    if not os.path.exists(input_path):
        raise FileNotFoundError(f"The path '{input_path}' does not exist.")

    # Create output directory if it doesn't exist and save_images is True
    if save_images and output_dir:
        ensure_directory_exists(output_dir)

    result = {}

    # Handle single PDF file
    if os.path.isfile(input_path):
        if not input_path.lower().endswith('.pdf'):
            raise ValueError(f"The file '{input_path}' is not a PDF file.")

        pdf_files = [input_path]

    # Handle directory of PDF files
    elif os.path.isdir(input_path):
        pdf_files = glob(os.path.join(input_path, "**", "*.pdf"), recursive=True)
        if not pdf_files:
            raise ValueError(f"No PDF files found in '{input_path}'.")

    else:
        raise ValueError(f"The path '{input_path}' is neither a file nor a directory.")

    # Set default crop coordinates if not provided
    if crop_images and crop_coords is None:
        crop_coords = DEFAULT_CROP_COORDS

    # Process each PDF file
    for pdf_path in tqdm(pdf_files, desc="Converting PDFs to images"):
        try:
            # Get the base filename without extension
            pdf_filename = os.path.basename(pdf_path)
            pdf_name = os.path.splitext(pdf_filename)[0]

            # Convert PDF to images
            images = convert_from_path(pdf_path, dpi=dpi)
            processed_images = []

            # Process each image (crop if requested)
            for i, image in enumerate(images):
                # Create filename for the image
                image_filename = f"{pdf_name}_page_{i+1}.{output_format}"

                # Apply cropping if requested
                if crop_images:
                    processed_image = crop_image(image, crop_coords)
                else:
                    processed_image = image

                # Save the image if requested
                if save_images and output_dir:
                    # Determine the output directory based on organize_by_pdf
                    if organize_by_pdf:
                        # Create a subdirectory for this PDF
                        pdf_output_dir = get_output_directory_for_pdf(output_dir, pdf_name)
                        image_path = os.path.join(pdf_output_dir, image_filename)
                    else:
                        image_path = os.path.join(output_dir, image_filename)

                    # Get the correct format string for PIL
                    format_str = FORMAT_MAPPING.get(output_format.lower(), 'JPEG')
                    processed_image.save(image_path, format_str)

                processed_images.append(processed_image)

            # Store the processed images in the result dictionary
            result[pdf_filename] = processed_images

        except Exception as e:
            print(f"Error processing '{pdf_path}': {str(e)}")

    return result

if __name__ == "__main__":
    # Example usage

    # Get a sample PDF from the default directory
    # sample_pdf = get_sample_pdf()
    # print(f"Using sample PDF: {sample_pdf}")

    # Convert a single PDF file with default settings (full-size images)
    # convert_pdf_to_images(sample_pdf)

    # Convert all PDFs in the default directory with default settings
    # convert_pdf_to_images()

    # Convert a sample PDF and crop the images with default crop ratios
    # sample_pdf = get_sample_pdf()
    # convert_pdf_to_images(sample_pdf, crop_images=True)

    # Convert a PDF and crop the images with custom crop coordinates
    # Custom crop coordinates in format [(x1, y1), (x2, y2)]
    # Where (x1, y1) is the top-left corner and (x2, y2) is the bottom-right corner
    # custom_crop_coords = [(0.10, 0.15), (0.87, 0.90)]  # Crop 15% from top, 10% from bottom, 10% from left, 13% from right
    # sample_pdf = get_sample_pdf()
    # convert_pdf_to_images(sample_pdf, crop_images=True, crop_coords=custom_crop_coords)

    # Convert a PDF without saving the images (just return them)
    # sample_pdf = get_sample_pdf()
    # images_dict = convert_pdf_to_images(sample_pdf, save_images=False)
    # for pdf_name, images in images_dict.items():
    #     print(f"PDF: {pdf_name}, Number of pages: {len(images)}")

    # Example of actual usage
    try:
        # Get a sample PDF
        sample_pdf = get_sample_pdf()
        print(f"Converting sample PDF: {os.path.basename(sample_pdf)}")

        # Create output directories for full-size and cropped images
        full_images_dir = os.path.join(DEFAULT_OUTPUT_DIR, "full_images")
        cropped_images_dir = os.path.join(DEFAULT_OUTPUT_DIR, "cropped_images")

        # Ensure the directories exist
        ensure_directory_exists(DEFAULT_OUTPUT_DIR)
        ensure_directory_exists(full_images_dir)
        ensure_directory_exists(cropped_images_dir)

        # Convert to images (both full-size and cropped)
        # full_images = convert_pdf_to_images(
        #     sample_pdf,
        #     output_dir=full_images_dir,
        #     organize_by_pdf=True
        # )

        # Define custom crop coordinates (optional)
        # Format: [(x1, y1), (x2, y2)] where values are relative (0.0 to 1.0)
        custom_crop_coords = [(0.10, 0.25), (0.87, 0.77)]  # Example: crop 10% from left, 15% from top, 10% from right/bottom

        cropped_images = convert_pdf_to_images(
            sample_pdf,
            output_dir=cropped_images_dir,
            crop_images=True,
            crop_coords=custom_crop_coords,  # Use custom coordinates (or remove this line to use defaults)
            organize_by_pdf=True
        )

        print(f"Conversion complete. Check the output directories:")
        print(f"  - Full-size images: {full_images_dir}")
        print(f"  - Cropped images: {cropped_images_dir}")
    except Exception as e:
        print(f"Error: {str(e)}")

    pass
