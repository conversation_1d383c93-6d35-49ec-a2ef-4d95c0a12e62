"""
Scratchpad
    Pre: None
    Post: A directory that contain the pool of images file

    Argument to concern: Pi<PERSON> type, Pile amount, Sitename, Filename, index
"""

import os
from pdf2image import convert_from_path
from typing import List, Tuple
import numpy as np
from glob import glob
from utils.helper import create_dir_if_not_exists
from tqdm import tqdm
import shutil
from natsort import natsorted

DATASET_NAME = "sonic_logging_fold_1"
RAW_DATA_BATCH = "Sonic_Logging_Pool_1_251024"

HIERACHY_INFORMATION = "./raw_data/Sonic_logging/Sonic_Logging_batch_1"
RAW_DATA_DIR = "./raw_data/"
RAW_DATA_POOL_DIR = "./raw_data/Sonic_logging/Sonic_Logging_Pool_1_251024"
DATA_DIR = os.path.join(
    RAW_DATA_DIR,
    f"Sonic_logging/{RAW_DATA_BATCH}"
)
TRAIN_DATA = "./train_data"
OUTPUT_DIR = os.path.join(
    TRAIN_DATA,
    DATASET_NAME
)

CROP_RATIO:dict = {
    "FROM_START_TO_UPPER" : 0.195,
    "FROM_END_TO_LOWER" : 0.08
}

def duplicate_site_information_to_pool():

    # Get overall before moving
    total_files = len([f_name for _, _, f_names in os.walk(HIERACHY_INFORMATION) for f_name in f_names if f_name.lower().endswith(".pdf")])
    
    with tqdm(total=total_files, desc="Process to moving files") as pbar:
        for dirpath, dirnames, filenames in os.walk(HIERACHY_INFORMATION):
            for filename in filenames:
                if (filename.lower().endswith(".pdf")):
                    source_path = os.path.join(dirpath, filename)
                    
                    parent_folder = os.path.basename(dirpath) # THis return the Defect, OK

                    target_dir = os.path.join(RAW_DATA_POOL_DIR, parent_folder)

                    create_dir_if_not_exists(target_dir)
                    target_path = os.path.join(target_dir, filename)

                    shutil.copy(source_path, target_path)
                    pbar.update(1)

def run_crop_and_save():
    """
    Read the pdf file from DATA_DIR and convert to image
    """
    
    create_dir_if_not_exists(OUTPUT_DIR)
    total_file_to_convert = len([f_name for _, _, f_names in os.walk(DATA_DIR) for f_name in f_names if f_name.lower().endswith(".pdf")])
    # pdf_arr = natsorted(glob(DATA_DIR + "/*.pdf"))[:10]

    with tqdm(total=total_file_to_convert, desc="Procesing PDF") as pbar:
        for dirpath, dirnames, filenames in os.walk(DATA_DIR):
            for filename in filenames:
                if (filename.lower().endswith(".pdf")):
                    source_path = os.path.join(dirpath, filename)
                    images = convert_from_path(source_path)

                    parent_dir = os.path.basename(dirpath)
                    source_filename:str = filename.split(".")[0]

                    for page_index, image in enumerate(images):
                        target_filename: str = f"{source_filename}_{parent_dir}_{page_index}.jpg"
                        crop_and_save_image(
                            image,
                            target_filename,
                        )
                    pbar.update(1)
            


def get_crop_ratio_from_image_size(
    image_size: Tuple
):
    w, h = image_size
    start_h = h * CROP_RATIO["FROM_START_TO_UPPER"]
    end_h = (1-CROP_RATIO["FROM_END_TO_LOWER"]) * h
    return [start_h, end_h]

def crop_and_save_image(
    image: object,
    target_filename: str) -> None:

    # height, width, channel = image.shape
    width, height = image.size
    start_h, end_h = get_crop_ratio_from_image_size((width, height))
    crop_box = (0, start_h, width, end_h)

    cropped_image = image.crop(crop_box)
    save_path = os.path.join(
        OUTPUT_DIR,
        target_filename
    )
    cropped_image.save(save_path, "JPEG")

if __name__ == "__main__":
    # duplicate_site_information_to_pool() 
    run_crop_and_save()