import os
import matplotlib.pyplot as plt
from collections import Counter
import random
import cv2

def create_dir_if_not_exists(dir_path):
    """Create a directory if it does not exist.

    Args:
        dir_path (str): The path of the directory to create.
    """
    try:
        # Create the directory if it doesn't exist
        os.makedirs(dir_path, exist_ok=True)
        # print(f"Directory '{dir_path}' created or already exists.")
    except Exception as e:
        print(f"An error occurred while creating the directory: {e}")



"""
    Pre : Data is stored under data_dir in subfolders images/train, images/val, and images/test
    Post: Shows a bar plot of the distribution of file instances from each folder
          and displays the file counts for each folder and the overall total.
"""
def check_file_distribution(data_dir):
    # Define subfolders
    subfolders = ['images/train', 'images/val', 'images/test']
    
    # Count files in each folder
    file_counts = {}
    total_files = 0
    for subfolder in subfolders:
        folder_path = os.path.join(data_dir, subfolder)
        if os.path.exists(folder_path):
            # Count .jpg files in the folder
            count = len([f for f in os.listdir(folder_path) if f.endswith('.jpg')])
            file_counts[subfolder.split('/')[-1]] = count
            total_files += count
        else:
            print(f"Warning: {folder_path} does not exist.")
            file_counts[subfolder.split('/')[-1]] = 0

    # Plot distribution
    plt.figure(figsize=(8, 6))
    bars = plt.bar(file_counts.keys(), file_counts.values(), color=['blue', 'orange', 'green'])
    plt.title(f'File Distribution in Train, Validation, and Test Folders\nTotal Files: {total_files}')
    plt.xlabel('Folder')
    plt.ylabel('Number of Files')
    plt.grid(axis='y', linestyle='--', alpha=0.7)

    # Add the file count on top of each bar
    for bar in bars:
        yval = bar.get_height()
        plt.text(bar.get_x() + bar.get_width() / 2, yval + 10, f'{int(yval)}', ha='center', va='bottom')

    # Show the plot
    plt.show()

def plot_class_distribution(data_dir, subset='train'):
    """
    Plots the distribution of classes in YOLO format labels with precise counts annotated on the graph.

    Parameters:
    - data_dir (str): Path to the data directory containing 'labels/{subset}'.
    - subset (str): Subset to analyze ('train', 'val', 'test').
    
    Post:
    - Displays a bar plot showing the distribution of classes with counts annotated.
    """
    # Define the path for label files
    label_folder = os.path.join(data_dir, f'labels/{subset}')
    
    # Get all label files (assumes .txt labels corresponding to .jpg images)
    label_files = [f for f in os.listdir(label_folder) if f.endswith('.txt')]
    
    # Define class mapping
    class_mapping = {
        0: "Defect_B",
        1: "Defect_C",
        2: "Defect_D"
    }
    
    # Create a Counter to keep track of class occurrences
    class_counter = Counter()
    
    # Iterate over all label files
    for label_file in label_files:
        label_path = os.path.join(label_folder, label_file)
        
        # Read the label file
        with open(label_path, 'r') as f:
            labels = f.readlines()
        
        # Count the classes in the label file
        for label in labels:
            parts = label.strip().split()
            class_id = int(float(parts[0]))  # First item in the line is the class ID
            class_counter[class_id] += 1
    
    # Extract the class IDs and their counts
    class_ids = list(class_counter.keys())
    counts = list(class_counter.values())
    
    # Map class IDs to class names using the class_mapping
    class_names = [class_mapping.get(class_id, f"Class_{class_id}") for class_id in class_ids]
    
    # Calculate the total occurrences and total files
    total_occurrences = sum(counts)
    total_files = len(label_files)
    
    # Calculate average occurrences per class and per file
    avg_occurrences_per_class = total_occurrences / len(class_ids) if len(class_ids) > 0 else 0
    avg_occurrences_per_file = total_occurrences / total_files if total_files > 0 else 0
    
    # Print the average class/file numbers
    print(f"Average occurrences per class: {avg_occurrences_per_class:.2f}")
    print(f"Average occurrences per file: {avg_occurrences_per_file:.2f}")
    
    # Plot the class distribution
    plt.figure(figsize=(10, 6))
    bars = plt.bar(class_names, counts, color='skyblue', alpha=0.8, edgecolor='black')
    
    # Annotate the bars with precise counts
    for bar, count in zip(bars, counts):
        plt.text(
            bar.get_x() + bar.get_width() / 2,
            bar.get_height() - 0.1,
            str(count),
            ha='center',
            va='bottom',
            fontsize=12,
            color='black',
            weight='bold'
        )
    
    plt.title(f'Class Distribution in {subset.capitalize()} Labels', fontsize=16)
    plt.xlabel('Class', fontsize=14)
    plt.ylabel('Number of Occurrences', fontsize=14)
    plt.xticks(rotation=45, ha='right', fontsize=12)
    plt.yticks(fontsize=12)
    plt.grid(axis='y', linestyle='--', alpha=0.7)
    plt.tight_layout()  # Ensure everything fits within the figure
    plt.show()


def count_and_plot_yolo_train_data(label_folder):
    """
    Counts and plots the distribution of positive and negative samples
    in a YOLO training folder.

    Parameters:
    - label_folder (str): Path to the training folder containing `.txt` label files.

    Returns:
    - dict: Dictionary with counts of positive and negative samples.
    """
    positive_count = 0
    negative_count = 0

    # List all label files in the folder
    label_files = [f for f in os.listdir(label_folder) if f.endswith('.txt')]

    for label_file in label_files:
        label_path = os.path.join(label_folder, label_file)

        # Check if the label file has content
        with open(label_path, 'r') as f:
            content = f.read().strip()

        if content:
            positive_count += 1  # File has content, it's a positive sample
        else:
            negative_count += 1  # File is empty, it's a negative sample

    # Plot the distribution
    labels = ['Positive', 'Negative']
    counts = [positive_count, negative_count]
    colors = ['#76c7c0', '#f08080']

    plt.figure(figsize=(8, 6))
    bars = plt.bar(labels, counts, color=colors, alpha=0.8, edgecolor='black')

    # Annotate the bars with precise counts
    for bar, count in zip(bars, counts):
        plt.text(
            bar.get_x() + bar.get_width() / 2,
            bar.get_height() - 0.1,
            f'{count}',
            ha='center',
            va='bottom',
            fontsize=12,
            color='black',
            weight='bold'
        )

    plt.title("Distribution of YOLO Training Data", fontsize=16)
    plt.ylabel("Count", fontsize=14)
    plt.xlabel("Sample Type", fontsize=14)
    plt.xticks(fontsize=12)
    plt.yticks(fontsize=12)
    plt.grid(axis='y', linestyle='--', alpha=0.7)
    plt.tight_layout()
    plt.show()

    return {
        "positive": positive_count,
        "negative": negative_count
    }


def query_class_from_pool(
        data_dir: str,
        sub_set: str = "train",
        class_to_query: int | str = "0"):
    """
    Queries information from the YOLO folder hierarchy and visualizes images with bounding boxes.

    Parameters:
    - data_dir (str): Base directory containing `images` and `labels` folders.
    - sub_set (str): Subdirectory under `images` and `labels` (e.g., "train" or "val").
    - class_to_query (int | str): Class ID to query (default is "0").
    """
    # Define paths
    images_dir = os.path.join(data_dir, f'images/{sub_set}')
    labels_dir = os.path.join(data_dir, f'labels/{sub_set}')

    if not os.path.exists(images_dir) or not os.path.exists(labels_dir):
        print(f"Invalid directories: {images_dir} or {labels_dir}")
        return

    # Query label files for the specific class
    matching_files = []
    for label_file in os.listdir(labels_dir):
        label_path = os.path.join(labels_dir, label_file)
        with open(label_path, 'r') as file:
            for line in file:
                parts = line.strip().split()
                if len(parts) > 0 and parts[0] == str(class_to_query):
                    matching_files.append((label_file, line))
                    break

    if not matching_files:
        print(f"No matching files found for class {class_to_query} in {sub_set}")
        return

    print(f"Found {len(matching_files)} files containing class {class_to_query}")

    # Randomly select three samples
    selected_files = random.sample(matching_files, min(3, len(matching_files)))

    # Set up the plot
    fig, axes = plt.subplots(1, 3, figsize=(15, 5))
    for ax, (label_file, _) in zip(axes, selected_files):
        base_name = os.path.splitext(label_file)[0]
        image_path = os.path.join(images_dir, base_name + '.jpg')
        label_path = os.path.join(labels_dir, label_file)

        if not os.path.exists(image_path):
            print(f"Image file not found for {image_path}")
            continue

        # Load the image
        image = cv2.imread(image_path)
        image = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)

        # Read and parse the bounding boxes
        boxes = []
        with open(label_path, 'r') as file:
            for line in file:
                parts = line.strip().split()
                if len(parts) >= 5:
                    class_id = parts[0]
                    if class_id == str(class_to_query):
                        x_center, y_center, width, height = map(float, parts[1:])
                        boxes.append((x_center, y_center, width, height))

        # Draw bounding boxes
        height, width, _ = image.shape
        for x_center, y_center, w, h in boxes:
            x1 = int((x_center - w / 2) * width)
            y1 = int((y_center - h / 2) * height)
            x2 = int((x_center + w / 2) * width)
            y2 = int((y_center + h / 2) * height)
            cv2.rectangle(image, (x1, y1), (x2, y2), (255, 0, 0), 4)

        # Plot the image
        ax.imshow(image)
        ax.set_title(f"Class {class_to_query} - {base_name}")
        ax.axis("off")

    # Adjust layout and show the plot
    plt.tight_layout()
    plt.show()


def check_raw_label_from_fold(base_dir: str):
    """
    Label in format of ../data/labelled_data/sonic-logging-labelled/<Fold_n>
    Example:
        ../data/labelled_data/sonic-logging-labelled/Fold_1/obj_train_data/<label.txt> file

    Post: Walk through all "Fold_n" folders and plot the distribution of "positive" and "negative"
    label files from each fold.
    
    Positive: .txt file contains YOLO content.
    Negative: .txt file does not contain any content.
    """
    import matplotlib.pyplot as plt
    import os

    fold_dirs = [os.path.join(base_dir, d) for d in os.listdir(base_dir) if os.path.isdir(os.path.join(base_dir, d)) and d.startswith("Fold_")]

    fold_results = {}
    total_positive = 0
    total_negative = 0
    fold_positive_counts = []
    fold_negative_counts = []
    fold_names = []

    for fold_dir in fold_dirs:
        label_dir = os.path.join(fold_dir, "obj_train_data")
        print("Check label dir", label_dir)
        if not os.path.exists(label_dir):
            print(f"Label directory not found in {fold_dir}, skipping...")
            continue
        
        # Process label files in the fold
        label_files = [f for f in os.listdir(label_dir) if f.endswith('.txt')]
        positive_count = 0
        negative_count = 0

        for label_file in label_files:
            label_path = os.path.join(label_dir, label_file)
            with open(label_path, 'r') as f:
                content = f.read().strip()
                if content:
                    positive_count += 1
                else:
                    negative_count += 1

        fold_results[fold_dir] = (positive_count, negative_count)
        total_positive += positive_count
        total_negative += negative_count
        fold_positive_counts.append(positive_count)
        fold_negative_counts.append(negative_count)
        fold_names.append(os.path.basename(fold_dir))

    # Plot the distribution for each fold
    for fold, (positive, negative) in fold_results.items():
        labels = ['Positive', 'Negative']
        counts = [positive, negative]

        plt.bar(labels, counts, color=['green', 'red'])
        plt.title(f"Distribution in {os.path.basename(fold)}")
        plt.ylabel("Count")
        plt.show()

        print(f"{os.path.basename(fold)} - Positive: {positive}, Negative: {negative}")

    # Plot the overall distribution for all folds together
    x_indices = range(len(fold_names))
    bar_width = 0.4

    plt.bar(x_indices, fold_positive_counts, bar_width, label='Positive', color='green')
    plt.bar([x + bar_width for x in x_indices], fold_negative_counts, bar_width, label='Negative', color='red')
    plt.xticks([x + bar_width / 2 for x in x_indices], fold_names, rotation=45)
    plt.title("Positive vs Negative Labels Across All Folds")
    plt.xlabel("Fold")
    plt.ylabel("Count")
    plt.legend()
    plt.tight_layout()
    plt.show()

    # Print overall distribution
    print("\nOverall Distribution:")
    print(f"Total Positive (with content): {total_positive}")
    print(f"Total Negative (empty): {total_negative}")


def plot_raw_class_distribution(base_dir: str):
    """
    Reads YOLO label files from multiple folds and plots the class distribution as a bar chart.

    Args:
        base_dir (str): Base directory containing "Fold_n" folders with YOLO label files.

    Note:
        The YOLO label format is: <class> <x_center> <y_center> <width> <height>
        Classes: {0: 'defect_B', 1: 'defect_C', 2: 'defect_D'}
    """
    import os
    import matplotlib.pyplot as plt
    from collections import defaultdict

    # Class mapping
    class_map = {0: 'defect_B', 1: 'defect_C', 2: 'defect_D'}
    class_counts = defaultdict(int)  # Count instances of each class across all folds

    # Get all Fold directories
    fold_dirs = [os.path.join(base_dir, d) for d in os.listdir(base_dir) if os.path.isdir(os.path.join(base_dir, d)) and d.startswith("Fold_")]

    for fold_dir in fold_dirs:
        label_dir = os.path.join(fold_dir, "obj_train_data")

        if not os.path.exists(label_dir):
            print(f"Label directory not found in {fold_dir}, skipping...")
            continue

        # Process label files
        label_files = [f for f in os.listdir(label_dir) if f.endswith('.txt')]

        for label_file in label_files:
            label_path = os.path.join(label_dir, label_file)
            with open(label_path, 'r') as f:
                lines = f.readlines()

                for line in lines:
                    # YOLO format: <class> <x_center> <y_center> <width> <height>
                    class_id = int(line.split()[0])  # Extract the class ID
                    class_counts[class_id] += 1

    # Map class IDs to names for the plot
    class_names = [class_map.get(i, f"Class_{i}") for i in sorted(class_counts.keys())]
    counts = [class_counts[i] for i in sorted(class_counts.keys())]

    # Plot the distribution
    plt.bar(class_names, counts, color=['blue', 'orange', 'red'])
    plt.title("Class Distribution Across All Folds")
    plt.xlabel("Class")
    plt.ylabel("Instance Count")
    plt.xticks(rotation=45)
    plt.tight_layout()
    plt.show()

    # Print counts for clarity
    print("\nClass Distribution:")
    for class_id, count in class_counts.items():
        print(f"{class_map.get(class_id, f'Class_{class_id}')}: {count}")
