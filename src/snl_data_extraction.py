import os
import shutil
from tqdm import tqdm
from collections import defaultdict
import matplotlib.pyplot as plt
import numpy as np
from typing import List, Tuple

# Constants
IMAGE_POOLS = "../data/process_data/[SNL]_data/snl_image_pool"
LABEL_POOLS = "../data/process_data/[SNL]_data/labels_pool"
LABEL_POOL_SOURCE = "../LABEL_POOL"  # Adjust this path to your actual LABEL_POOL location

def extract_label_files_from_label_pool(source_dir=LABEL_POOL_SOURCE, destination_dir=LABEL_POOLS):
    """
    Extract .txt files from LABEL_POOL structure and move them to destination.
    
    Args:
        source_dir (str): Path to LABEL_POOL directory containing SNL_fold_*_label folders
        destination_dir (str): Destination path "../data/process_data/[SNL]_data/labels_pool"
    
    Returns:
        dict: Statistics about moved files
    """
    # Create destination directory if it doesn't exist
    os.makedirs(destination_dir, exist_ok=True)
    
    stats = {
        'total_files_moved': 0,
        'files_per_fold': defaultdict(int),
        'errors': []
    }
    
    # Iterate through SNL_fold_1_label to SNL_fold_14_label
    for fold_num in range(1, 15):
        fold_name = f"SNL_fold_{fold_num}_label"
        fold_path = os.path.join(source_dir, fold_name)
        obj_train_data_path = os.path.join(fold_path, "obj_train_data")
        
        if not os.path.exists(obj_train_data_path):
            stats['errors'].append(f"Directory not found: {obj_train_data_path}")
            continue
        
        # Get all .txt files in obj_train_data directory
        txt_files = [f for f in os.listdir(obj_train_data_path) if f.endswith('.txt')]
        
        if not txt_files:
            stats['errors'].append(f"No .txt files found in: {obj_train_data_path}")
            continue
        
        # Move files with progress bar
        for txt_file in tqdm(txt_files, desc=f"Moving files from {fold_name}"):
            source_file = os.path.join(obj_train_data_path, txt_file)
            destination_file = os.path.join(destination_dir, txt_file)
            
            try:
                # If file already exists, create a unique name
                if os.path.exists(destination_file):
                    base_name, ext = os.path.splitext(txt_file)
                    counter = 1
                    while os.path.exists(destination_file):
                        new_name = f"{base_name}_fold{fold_num}_{counter}{ext}"
                        destination_file = os.path.join(destination_dir, new_name)
                        counter += 1
                
                shutil.move(source_file, destination_file)
                stats['total_files_moved'] += 1
                stats['files_per_fold'][fold_name] += 1
                
            except Exception as e:
                stats['errors'].append(f"Error moving {source_file}: {str(e)}")
    
    return stats

def check_positive_negative_samples(labels_dir):
    """
    Check for positive and negative samples in label files.
    
    Args:
        labels_dir (str): Directory containing .txt label files
    
    Returns:
        dict: Statistics about positive and negative samples
    """
    if not os.path.exists(labels_dir):
        print(f"Labels directory not found: {labels_dir}")
        return None
    
    # Get all .txt files
    txt_files = [f for f in os.listdir(labels_dir) if f.endswith('.txt')]
    
    if not txt_files:
        print(f"No .txt files found in: {labels_dir}")
        return None
    
    stats = {
        'positive_samples': [],
        'negative_samples': [],
        'positive_count': 0,
        'negative_count': 0,
        'total_files': len(txt_files),
        'errors': []
    }
    
    # Check each file
    for txt_file in tqdm(txt_files, desc="Checking label files"):
        file_path = os.path.join(labels_dir, txt_file)
        
        try:
            with open(file_path, 'r') as f:
                content = f.read().strip()
                
            if content:  # File has content (positive sample)
                stats['positive_samples'].append(txt_file)
                stats['positive_count'] += 1
            else:  # File is empty (negative sample)
                stats['negative_samples'].append(txt_file)
                stats['negative_count'] += 1
                
        except Exception as e:
            stats['errors'].append(f"Error reading {txt_file}: {str(e)}")
    
    return stats

def print_sample_statistics(stats):
    """
    Print formatted statistics about positive and negative samples.
    
    Args:
        stats (dict): Statistics dictionary from check_positive_negative_samples
    """
    if not stats:
        return
    
    print("=" * 50)
    print("LABEL SAMPLE STATISTICS")
    print("=" * 50)
    print(f"Total files analyzed: {stats['total_files']}")
    print(f"Positive samples (with content): {stats['positive_count']}")
    print(f"Negative samples (empty): {stats['negative_count']}")
    print(f"Positive percentage: {(stats['positive_count'] / stats['total_files'] * 100):.2f}%")
    print(f"Negative percentage: {(stats['negative_count'] / stats['total_files'] * 100):.2f}%")
    
    if stats['errors']:
        print(f"\nErrors encountered: {len(stats['errors'])}")
        for error in stats['errors'][:5]:  # Show first 5 errors
            print(f"  - {error}")
        if len(stats['errors']) > 5:
            print(f"  ... and {len(stats['errors']) - 5} more errors")
    
    print("\nFirst 10 positive samples:")
    for sample in stats['positive_samples'][:10]:
        print(f"  - {sample}")
    
    print("\nFirst 10 negative samples:")
    for sample in stats['negative_samples'][:10]:
        print(f"  - {sample}")

def analyze_class_instances(labels_dir):
    """
    Analyze class instances from .txt label files.

    Args:
        labels_dir (str): Directory containing .txt label files

    Returns:
        dict: Statistics about class instances
    """
    # Class mapping
    class_mapping = {
        0: 'defect_B',
        1: 'defect_C',
        2: 'defect_D'  # Note: both 1 and 2 map to defect_C as specified
    }

    if not os.path.exists(labels_dir):
        print(f"Labels directory not found: {labels_dir}")
        return None

    # Get all .txt files
    txt_files = [f for f in os.listdir(labels_dir) if f.endswith('.txt')]

    if not txt_files:
        print(f"No .txt files found in: {labels_dir}")
        return None

    stats = {
        'class_counts': defaultdict(int),
        'class_files': defaultdict(list),
        'total_annotations': 0,
        'files_with_annotations': 0,
        'files_without_annotations': 0,
        'errors': []
    }

    # Analyze each file
    for txt_file in tqdm(txt_files, desc="Analyzing class instances"):
        file_path = os.path.join(labels_dir, txt_file)
        file_has_annotations = False

        try:
            with open(file_path, 'r') as f:
                lines = f.readlines()

            for line in lines:
                line = line.strip()
                if line:  # Non-empty line
                    file_has_annotations = True
                    parts = line.split()
                    if len(parts) >= 1:
                        try:
                            class_id = int(parts[0])
                            class_name = class_mapping.get(class_id, f'unknown_class_{class_id}')
                            stats['class_counts'][class_name] += 1
                            stats['class_files'][class_name].append(txt_file)
                            stats['total_annotations'] += 1
                        except ValueError:
                            stats['errors'].append(f"Invalid class ID in {txt_file}: {parts[0]}")

            if file_has_annotations:
                stats['files_with_annotations'] += 1
            else:
                stats['files_without_annotations'] += 1

        except Exception as e:
            stats['errors'].append(f"Error reading {txt_file}: {str(e)}")

    return stats

def plot_positive_negative_distribution(sample_stats):
    """
    Plots a bar chart showing the distribution of positive and negative samples.

    Args:
        sample_stats (dict): A dictionary containing statistics,
        expected to have 'positive_count', 'negative_count',
        and 'total_files' keys.
        Example: {'positive_count': 100, 'negative_count': 50, 'total_files': 150}
    """
    if not isinstance(sample_stats, dict):
        print("Error: sample_stats must be a dictionary.")
        return
    if 'positive_count' not in sample_stats or \
       'negative_count' not in sample_stats or \
       'total_files' not in sample_stats:
        print("Error: sample_stats dictionary is missing required keys "
              "('positive_count', 'negative_count', 'total_files').")
        return
    if not sample_stats:
        print("No sample statistics provided or dictionary is empty.")
        return
    if sample_stats['total_files'] == 0:
        print("Total files is zero, cannot plot distribution.")
        return

    # Data for plotting
    categories = ['Positive Samples\n(with content)', 'Negative Samples\n(empty)']
    counts = [sample_stats['positive_count'], sample_stats['negative_count']]
    colors = ['#2E8B57', '#DC143C']  # Sea green for positive, crimson for negative

    # Create the plot
    plt.figure(figsize=(10, 6))
    bars = plt.bar(categories, counts, color=colors, alpha=0.8, edgecolor='black', linewidth=1.5)

    # Add value labels on bars with percentage
    for bar, count in zip(bars, counts):
        height = bar.get_height()
        percentage = (count / sample_stats["total_files"]) * 100 if sample_stats["total_files"] > 0 else 0
        plt.text(bar.get_x() + bar.get_width() / 2., height + max(counts) * 0.02, # Adjusted vertical offset
                f'{count}\n({percentage:.1f}%)',
                ha='center', va='bottom', fontsize=10, fontweight='bold', color='darkslategray')

    # Customize the plot
    plt.title('Distribution of Positive vs Negative Label Samples', fontsize=18, fontweight='bold', pad=20, color='navy')
    plt.ylabel('Number of Files', fontsize=14, fontweight='semibold')
    plt.xlabel('Sample Type', fontsize=14, fontweight='semibold')
    plt.grid(axis='y', linestyle='--', alpha=0.6)
    plt.xticks(fontsize=12)
    plt.yticks(fontsize=12)

    # Add total count annotation
    # Placing it relative to the axes to ensure it's always visible
    plt.text(0.5, 0.95, f'Total Files: {sample_stats["total_files"]}',
             transform=plt.gca().transAxes, ha='center', fontsize=12,
             bbox=dict(boxstyle="round,pad=0.4", facecolor="#ADD8E6", alpha=0.8, edgecolor='steelblue', linewidth=1)) # Light blue box

    # Improve layout and display
    plt.tight_layout(rect=[0, 0, 1, 0.95]) # Adjust rect to make space for title if needed
    plt.show()

def plot_class_distribution(class_stats):
    """
    Plot bar chart showing distribution of different defect classes.

    Args:
        class_stats (dict): Statistics from analyze_class_instances()
    """
    if not class_stats or not class_stats['class_counts']:
        print("No class statistics provided or no classes found")
        return

    # Prepare data for plotting
    classes = list(class_stats['class_counts'].keys())
    counts = list(class_stats['class_counts'].values())

    # Color mapping for different classes
    color_map = {
        'defect_B': '#FF6B6B',  # Light red
        'defect_C': '#4ECDC4',  # Teal
    }
    colors = [color_map.get(cls, '#95A5A6') for cls in classes]  # Default gray for unknown

    # Create the plot
    plt.figure(figsize=(12, 7))
    bars = plt.bar(classes, counts, color=colors, alpha=0.7, edgecolor='black', linewidth=1)

    # Add value labels on bars
    for bar, count in zip(bars, counts):
        height = bar.get_height()
        plt.text(bar.get_x() + bar.get_width()/2., height + max(counts)*0.01,
                f'{count}\n({count/class_stats["total_annotations"]*100:.1f}%)',
                ha='center', va='bottom', fontweight='bold')

    # Customize the plot
    plt.title('Distribution of Defect Classes in Label Files', fontsize=16, fontweight='bold', pad=20)
    plt.ylabel('Number of Annotations', fontsize=12)
    plt.xlabel('Defect Class', fontsize=12)
    plt.grid(axis='y', alpha=0.3)

    # Add statistics annotation
    stats_text = f'Total Instances: {class_stats["total_annotations"]}\n'
    stats_text += f'Files with Annotations: {class_stats["files_with_annotations"]}\n'
    stats_text += f'Files without Annotations: {class_stats["files_without_annotations"]}'

    plt.text(0.02, 0.98, stats_text, transform=plt.gca().transAxes,
             verticalalignment='top', fontsize=10,
             bbox=dict(boxstyle="round,pad=0.5", facecolor="lightblue", alpha=0.7))

    plt.xticks(rotation=45, ha='right')
    plt.tight_layout()
    plt.show()

def print_class_statistics(class_stats):
    """
    Print detailed statistics about class instances.

    Args:
        class_stats (dict): Statistics from analyze_class_instances()
    """
    if not class_stats:
        return

    print("=" * 60)
    print("CLASS INSTANCE STATISTICS")
    print("=" * 60)
    print(f"Total annotations: {class_stats['total_annotations']}")
    print(f"Files with annotations: {class_stats['files_with_annotations']}")
    print(f"Files without annotations: {class_stats['files_without_annotations']}")

    print("\nClass distribution:")
    for class_name, count in class_stats['class_counts'].items():
        percentage = (count / class_stats['total_annotations'] * 100) if class_stats['total_annotations'] > 0 else 0
        print(f"  {class_name}: {count} annotations ({percentage:.2f}%)")
        print(f"    Found in {len(class_stats['class_files'][class_name])} files")

    if class_stats['errors']:
        print(f"\nErrors encountered: {len(class_stats['errors'])}")
        for error in class_stats['errors'][:5]:  # Show first 5 errors
            print(f"  - {error}")
        if len(class_stats['errors']) > 5:
            print(f"  ... and {len(class_stats['errors']) - 5} more errors")

def filter_top_defect_d_labels(source_labels_dir, output_folder_name, top_percentage=20, dry_run=True):
    """
    Filter out defect_D (class 2) labels that are located in the top percentage of the image.
    Copy filtered labels to a new folder.

    Args:
        source_labels_dir (str): Directory containing source .txt label files
        output_folder_name (str): Name of the output folder (will be created under ../data/process_data/[SNL]_data/for_training/)
        top_percentage (float): Percentage of top area to filter out (default: 20%)
        dry_run (bool): If True, only show what would be processed without actually creating files

    Returns:
        dict: Statistics about the filtering operation
    """
    # Class mapping for reference
    class_mapping = {
        0: 'defect_B',
        1: 'defect_C',
        2: 'defect_D'  # This is what we're filtering
    }

    # Create output directory path
    output_base_dir = "../data/process_data/[SNL]_data/for_training"
    output_dir = os.path.join(output_base_dir, output_folder_name, "labels_no_top_D")

    if not dry_run:
        os.makedirs(output_dir, exist_ok=True)

    if not os.path.exists(source_labels_dir):
        print(f"Source labels directory not found: {source_labels_dir}")
        return None

    # Get all .txt files
    txt_files = [f for f in os.listdir(source_labels_dir) if f.endswith('.txt')]

    if not txt_files:
        print(f"No .txt files found in: {source_labels_dir}")
        return None

    stats = {
        'total_files_processed': 0,
        'files_with_top_defect_d': 0,
        'files_without_top_defect_d': 0,
        'total_defect_d_removed': 0,
        'total_annotations_kept': 0,
        'processed_files': [],
        'skipped_files': [],
        'errors': []
    }

    top_threshold = top_percentage / 100.0  # Convert percentage to decimal

    print(f"Filtering defect_D (class 2) labels in top {top_percentage}% of images...")
    print(f"Source: {source_labels_dir}")
    print(f"Output: {output_dir}")
    print(f"Dry run: {dry_run}")

    # Process each file
    for txt_file in tqdm(txt_files, desc="Processing label files"):
        stats['total_files_processed'] += 1
        source_file_path = os.path.join(source_labels_dir, txt_file)
        output_file_path = os.path.join(output_dir, txt_file)

        try:
            # Read the label file
            with open(source_file_path, 'r') as f:
                lines = f.readlines()

            filtered_lines = []
            defect_d_removed_count = 0
            file_has_top_defect_d = False

            for line in lines:
                line = line.strip()
                if not line:  # Skip empty lines
                    continue

                parts = line.split()
                if len(parts) >= 5:  # Valid YOLO format: class x_center y_center width height
                    try:
                        class_id = int(parts[0])
                        # x_center = float(parts[1])  # Not needed for top filtering
                        y_center = float(parts[2])
                        # width = float(parts[3])     # Not needed for top filtering
                        # height = float(parts[4])    # Not needed for top filtering

                        # Check if this is defect_D (class 2) in top area
                        if class_id == 2 and y_center <= top_threshold:
                            # This is defect_D in the top area - remove it
                            defect_d_removed_count += 1
                            file_has_top_defect_d = True
                            print(f"  Removing defect_D at y_center={y_center:.3f} (top {top_percentage}%) from {txt_file}")
                        else:
                            # Keep this annotation
                            filtered_lines.append(line + '\n')
                            stats['total_annotations_kept'] += 1

                    except ValueError as e:
                        # Invalid format, but keep the line
                        filtered_lines.append(line + '\n')
                        stats['errors'].append(f"Invalid format in {txt_file}: {line} - {str(e)}")
                else:
                    # Invalid format, but keep the line
                    filtered_lines.append(line + '\n')

            # Update statistics
            stats['total_defect_d_removed'] += defect_d_removed_count

            if file_has_top_defect_d:
                stats['files_with_top_defect_d'] += 1
                stats['processed_files'].append((txt_file, defect_d_removed_count))
            else:
                stats['files_without_top_defect_d'] += 1
                stats['skipped_files'].append(txt_file)

            # Save the filtered file
            if dry_run:
                print(f"Would save filtered file: {output_file_path} ({len(filtered_lines)} annotations)")
            else:
                with open(output_file_path, 'w') as f:
                    f.writelines(filtered_lines)

        except Exception as e:
            stats['errors'].append(f"Error processing {txt_file}: {str(e)}")

    return stats

def print_filter_statistics(stats):
    """
    Print formatted statistics about the defect_D filtering operation.

    Args:
        stats (dict): Statistics from filter_top_defect_d_labels()
    """
    if not stats:
        return

    print("\n" + "=" * 60)
    print("DEFECT_D FILTERING STATISTICS")
    print("=" * 60)
    print(f"Total files processed: {stats['total_files_processed']}")
    print(f"Files with top defect_D removed: {stats['files_with_top_defect_d']}")
    print(f"Files without top defect_D: {stats['files_without_top_defect_d']}")
    print(f"Total defect_D annotations removed: {stats['total_defect_d_removed']}")
    print(f"Total annotations kept: {stats['total_annotations_kept']}")

    if stats['processed_files']:
        print(f"\nFiles with defect_D removed (first 10):")
        for filename, removed_count in stats['processed_files'][:10]:
            print(f"  {filename}: {removed_count} defect_D removed")
        if len(stats['processed_files']) > 10:
            print(f"  ... and {len(stats['processed_files']) - 10} more files")

    if stats['errors']:
        print(f"\nErrors encountered: {len(stats['errors'])}")
        for error in stats['errors'][:5]:
            print(f"  - {error}")
        if len(stats['errors']) > 5:
            print(f"  ... and {len(stats['errors']) - 5} more errors")

def batch_filter_defect_d_for_training_folders(base_labels_dir, training_folders, top_percentage=20, dry_run=True):
    """
    Batch process multiple training folders to filter out top defect_D labels.

    Args:
        base_labels_dir (str): Base directory containing label files
        training_folders (list): List of folder names to process
        top_percentage (float): Percentage of top area to filter out
        dry_run (bool): If True, only show what would be processed

    Returns:
        dict: Combined statistics from all folders
    """
    print("=" * 70)
    print("BATCH DEFECT_D FILTERING FOR TRAINING FOLDERS")
    print("=" * 70)

    combined_stats = {
        'folder_stats': {},
        'total_files_processed': 0,
        'total_defect_d_removed': 0,
        'total_annotations_kept': 0,
        'total_errors': 0
    }

    for folder_name in training_folders:
        print(f"\n{'='*50}")
        print(f"Processing folder: {folder_name}")
        print(f"{'='*50}")

        folder_stats = filter_top_defect_d_labels(
            source_labels_dir=base_labels_dir,
            output_folder_name=folder_name,
            top_percentage=top_percentage,
            dry_run=dry_run
        )

        if folder_stats:
            combined_stats['folder_stats'][folder_name] = folder_stats
            combined_stats['total_files_processed'] += folder_stats['total_files_processed']
            combined_stats['total_defect_d_removed'] += folder_stats['total_defect_d_removed']
            combined_stats['total_annotations_kept'] += folder_stats['total_annotations_kept']
            combined_stats['total_errors'] += len(folder_stats['errors'])

            print_filter_statistics(folder_stats)

    # Print combined summary
    print(f"\n{'='*70}")
    print("COMBINED SUMMARY")
    print(f"{'='*70}")
    print(f"Total folders processed: {len(training_folders)}")
    print(f"Total files processed: {combined_stats['total_files_processed']}")
    print(f"Total defect_D removed: {combined_stats['total_defect_d_removed']}")
    print(f"Total annotations kept: {combined_stats['total_annotations_kept']}")
    print(f"Total errors: {combined_stats['total_errors']}")

    return combined_stats

def prepare_segmentation_data(labels_dir, image_pools_dir, output_dir,
                             validation_split=0.1, include_negative_samples=False,
                             negative_samples_percent=10, is_detected_focus=False,
                             sampling_ratio=1.0, seed=42, dry_run=True):
    """
    Prepare segmentation training data by converting bounding box labels to segmentation masks.

    Args:
        labels_dir (str): Directory containing .txt label files with bounding boxes
        image_pools_dir (str): Directory containing corresponding image files
        output_dir (str): Output directory for organized segmentation training data
        validation_split (float): Percentage of data for validation (default: 0.1 = 10%)
        include_negative_samples (bool): Whether to include negative samples
        negative_samples_percent (int): Percentage of negative samples relative to positive samples
        is_detected_focus (bool): If True, convert all class labels to 0 (focus detection mode)
        sampling_ratio (float): Ratio of data to sample (0.0-1.0). 1.0 uses all data, 0.3 uses 30% for experiments
        seed (int): Random seed for reproducible data splitting and sampling
        dry_run (bool): If True, only show what would be processed without actually creating files

    Returns:
        dict: Statistics about the segmentation data preparation

    Directory structure:
        output_dir/
        ├── images/
        │   ├── train/              # Training images (original)
        │   └── validation/         # Validation images (original)
        └── labels/
            ├── train/              # Training segmentation labels (YOLO format)
            └── validation/         # Validation segmentation labels (YOLO format)

    Note:
        - Converts bounding box labels to segmentation masks using prepare_yolo_masks()
        - Creates YOLO segmentation format: class x1 y1 x2 y2 ... xn yn
        - Maintains same number of annotations as original bounding box labels
    """
    import random
    import shutil
    import cv2

    # Set random seed for reproducible results
    random.seed(seed)
    np.random.seed(seed)

    if not os.path.exists(labels_dir):
        print(f"Labels directory not found: {labels_dir}")
        return None

    if not os.path.exists(image_pools_dir):
        print(f"Image pools directory not found: {image_pools_dir}")
        return None

    # Create output directories
    train_images_dir = os.path.join(output_dir, "images", "train")
    train_labels_dir = os.path.join(output_dir, "labels", "train")
    val_images_dir = os.path.join(output_dir, "images", "validation")
    val_labels_dir = os.path.join(output_dir, "labels", "validation")

    if not dry_run:
        for dir_path in [train_images_dir, train_labels_dir, val_images_dir, val_labels_dir]:
            os.makedirs(dir_path, exist_ok=True)

    # Initialize statistics
    stats = {
        'total_label_files': 0,
        'positive_samples': [],
        'negative_samples': [],
        'train_positive': [],
        'val_positive': [],
        'train_negative': [],
        'val_negative': [],
        'missing_images': [],
        'segmentation_success': 0,
        'segmentation_errors': [],
        'focus_detection_modified': 0,
        'total_masks_created': 0,
        'errors': []
    }

    print(f"Preparing segmentation training data...")
    print(f"Labels directory: {labels_dir}")
    print(f"Image pools directory: {image_pools_dir}")
    print(f"Output directory: {output_dir}")
    print(f"Validation split: {validation_split*100:.1f}%")
    print(f"Include negative samples: {include_negative_samples}")
    if include_negative_samples:
        print(f"Negative samples percentage: {negative_samples_percent}%")
    print(f"Focus detection mode: {is_detected_focus}")
    print(f"Random seed: {seed}")
    print(f"Dry run: {dry_run}")

    # Step 1: Identify positive and negative samples
    print("\nStep 1: Identifying positive and negative samples...")
    txt_files = [f for f in os.listdir(labels_dir) if f.endswith('.txt')]
    stats['total_label_files'] = len(txt_files)

    for txt_file in tqdm(txt_files, desc="Analyzing label files"):
        label_path = os.path.join(labels_dir, txt_file)

        try:
            with open(label_path, 'r') as f:
                content = f.read().strip()

            # Find corresponding image file
            base_name = os.path.splitext(txt_file)[0]
            image_file = None

            # Look for image with common extensions
            for ext in ['.jpg', '.jpeg', '.png', '.bmp', '.tiff']:
                potential_image = base_name + ext
                image_path = os.path.join(image_pools_dir, potential_image)
                if os.path.exists(image_path):
                    image_file = potential_image
                    break

            if not image_file:
                stats['missing_images'].append(txt_file)
                continue

            # Classify as positive or negative
            if content:  # Has content (positive)
                stats['positive_samples'].append((txt_file, image_file))
            else:  # Empty (negative)
                stats['negative_samples'].append((txt_file, image_file))

        except Exception as e:
            stats['errors'].append(f"Error processing {txt_file}: {str(e)}")

    print(f"Found {len(stats['positive_samples'])} positive samples")
    print(f"Found {len(stats['negative_samples'])} negative samples")
    print(f"Missing images: {len(stats['missing_images'])}")

    # Step 2: Apply sampling if requested
    if sampling_ratio < 1.0:
        print(f"\nStep 2: Applying sampling ratio {sampling_ratio:.1%}...")

        # Sample positive samples
        original_positive_count = len(stats['positive_samples'])
        sampled_positive_count = int(original_positive_count * sampling_ratio)
        if sampled_positive_count > 0:
            random.shuffle(stats['positive_samples'])
            stats['positive_samples'] = stats['positive_samples'][:sampled_positive_count]

        # Sample negative samples if they exist
        original_negative_count = len(stats['negative_samples'])
        sampled_negative_count = int(original_negative_count * sampling_ratio)
        if sampled_negative_count > 0 and stats['negative_samples']:
            random.shuffle(stats['negative_samples'])
            stats['negative_samples'] = stats['negative_samples'][:sampled_negative_count]

        print(f"Sampled positive: {len(stats['positive_samples'])} / {original_positive_count} ({len(stats['positive_samples'])/original_positive_count*100:.1f}%)")
        print(f"Sampled negative: {len(stats['negative_samples'])} / {original_negative_count} ({len(stats['negative_samples'])/original_negative_count*100:.1f}%)")

        # Update statistics
        stats['sampling_applied'] = True
        stats['original_positive_count'] = original_positive_count
        stats['original_negative_count'] = original_negative_count
        stats['sampled_positive_count'] = len(stats['positive_samples'])
        stats['sampled_negative_count'] = len(stats['negative_samples'])
    else:
        stats['sampling_applied'] = False

    # Step 3: Split positive samples into train/validation
    print("\nStep 3: Splitting positive samples...")
    random.shuffle(stats['positive_samples'])

    val_count = int(len(stats['positive_samples']) * validation_split)
    stats['val_positive'] = stats['positive_samples'][:val_count]
    stats['train_positive'] = stats['positive_samples'][val_count:]

    print(f"Training positive samples: {len(stats['train_positive'])}")
    print(f"Validation positive samples: {len(stats['val_positive'])}")

    # Step 4: Add negative samples if requested
    if include_negative_samples and stats['negative_samples']:
        print(f"\nStep 4: Adding negative samples ({negative_samples_percent}%)...")

        # Calculate how many negative samples to add
        train_neg_count = int(len(stats['train_positive']) * negative_samples_percent / 100)
        val_neg_count = int(len(stats['val_positive']) * negative_samples_percent / 100)

        total_neg_needed = train_neg_count + val_neg_count

        if total_neg_needed > len(stats['negative_samples']):
            print(f"Warning: Need {total_neg_needed} negative samples but only {len(stats['negative_samples'])} available")
            train_neg_count = int(len(stats['negative_samples']) * len(stats['train_positive']) / len(stats['positive_samples']))
            val_neg_count = len(stats['negative_samples']) - train_neg_count

        # Randomly select negative samples
        random.shuffle(stats['negative_samples'])
        stats['train_negative'] = stats['negative_samples'][:train_neg_count]
        stats['val_negative'] = stats['negative_samples'][train_neg_count:train_neg_count + val_neg_count]

        print(f"Training negative samples: {len(stats['train_negative'])}")
        print(f"Validation negative samples: {len(stats['val_negative'])}")

    # Step 5: Process files and create segmentation masks
    print(f"\nStep 5: Processing files and creating segmentation masks...")

    def process_segmentation_files(file_pairs, dest_images_dir, dest_labels_dir, category_name):
        """Helper function to process files and create segmentation masks"""
        processed_count = 0
        for label_file, image_file in tqdm(file_pairs, desc=f"Processing {category_name}"):
            try:
                src_label = os.path.join(labels_dir, label_file)
                src_image = os.path.join(image_pools_dir, image_file)
                dest_label = os.path.join(dest_labels_dir, label_file)
                dest_image = os.path.join(dest_images_dir, image_file)

                if dry_run:
                    print(f"Would process segmentation: {src_image} + {src_label} -> {dest_image} + {dest_label}")
                else:
                    # Copy original image
                    shutil.copy2(src_image, dest_image)

                    # Load image for segmentation processing
                    rgb_image = cv2.imread(src_image)
                    if rgb_image is None:
                        stats['errors'].append(f"Could not load image: {src_image}")
                        continue

                    # Convert BGR to RGB (OpenCV loads as BGR)
                    rgb_image = cv2.cvtColor(rgb_image, cv2.COLOR_BGR2RGB)

                    try:
                        # Create segmentation masks using prepare_yolo_masks
                        absolute_positions = prepare_yolo_masks(rgb_image, src_label, is_return_image=False)

                        if absolute_positions is None or len(absolute_positions) == 0:
                            # No masks created, create empty label file
                            with open(dest_label, 'w') as f:
                                f.write("")
                        else:
                            # Convert absolute positions to YOLO segmentation format
                            segmentation_lines = []

                            # Read original label file to get class information
                            with open(src_label, 'r') as f:
                                original_lines = f.readlines()

                            # Get image dimensions for normalization
                            img_height, img_width = rgb_image.shape[:2]

                            # Process each mask
                            for i, polygon_points in enumerate(absolute_positions):
                                if len(polygon_points) < 3:  # Need at least 3 points for a polygon
                                    continue

                                # Get class ID from original label (if available)
                                class_id = 0  # Default class
                                if i < len(original_lines):
                                    original_line = original_lines[i].strip()
                                    if original_line:
                                        parts = original_line.split()
                                        if len(parts) >= 1:
                                            class_id = int(parts[0])

                                # Apply focus detection if enabled
                                if is_detected_focus:
                                    class_id = 0

                                # Convert absolute coordinates to normalized YOLO format
                                normalized_points = []
                                for point in polygon_points:
                                    x, y = point
                                    norm_x = x / img_width
                                    norm_y = y / img_height
                                    # Clamp to [0, 1] range
                                    norm_x = max(0, min(1, norm_x))
                                    norm_y = max(0, min(1, norm_y))
                                    normalized_points.extend([norm_x, norm_y])

                                # Create YOLO segmentation line: class x1 y1 x2 y2 ... xn yn
                                points_str = ' '.join([f"{coord:.6f}" for coord in normalized_points])
                                segmentation_line = f"{class_id} {points_str}\n"
                                segmentation_lines.append(segmentation_line)

                                stats['total_masks_created'] += 1

                            # Write segmentation label file
                            with open(dest_label, 'w') as f:
                                f.writelines(segmentation_lines)

                        stats['segmentation_success'] += 1

                        if is_detected_focus:
                            stats['focus_detection_modified'] += 1

                    except Exception as e:
                        stats['segmentation_errors'].append(f"Segmentation failed for {image_file}: {str(e)}")
                        # Create empty label file as fallback
                        with open(dest_label, 'w') as f:
                            f.write("")

                processed_count += 1

            except Exception as e:
                stats['errors'].append(f"Error processing {category_name} {label_file}: {str(e)}")

        return processed_count

    # Process training files
    train_pos_processed = process_segmentation_files(stats['train_positive'], train_images_dir, train_labels_dir, "train positive")
    train_neg_processed = process_segmentation_files(stats['train_negative'], train_images_dir, train_labels_dir, "train negative")

    # Process validation files
    val_pos_processed = process_segmentation_files(stats['val_positive'], val_images_dir, val_labels_dir, "validation positive")
    val_neg_processed = process_segmentation_files(stats['val_negative'], val_images_dir, val_labels_dir, "validation negative")

    # Update statistics
    stats['files_processed'] = {
        'train_positive': train_pos_processed,
        'train_negative': train_neg_processed,
        'val_positive': val_pos_processed,
        'val_negative': val_neg_processed,
        'total': train_pos_processed + train_neg_processed + val_pos_processed + val_neg_processed
    }

    return stats

def print_segmentation_data_statistics(stats):
    """
    Print formatted statistics about the segmentation data preparation.

    Args:
        stats (dict): Statistics from prepare_segmentation_data()
    """
    if not stats:
        return

    print("\n" + "=" * 70)
    print("SEGMENTATION DATA PREPARATION STATISTICS")
    print("=" * 70)

    print(f"Total label files analyzed: {stats['total_label_files']}")

    # Show sampling information if applicable
    if stats.get('sampling_applied', False):
        print(f"Original positive samples: {stats['original_positive_count']}")
        print(f"Original negative samples: {stats['original_negative_count']}")
        print(f"Sampled positive samples: {stats['sampled_positive_count']} ({stats['sampled_positive_count']/stats['original_positive_count']*100:.1f}%)")
        print(f"Sampled negative samples: {stats['sampled_negative_count']} ({stats['sampled_negative_count']/stats['original_negative_count']*100:.1f}%)")
    else:
        print(f"Positive samples found: {len(stats['positive_samples'])}")
        print(f"Negative samples found: {len(stats['negative_samples'])}")

    print(f"Missing image files: {len(stats['missing_images'])}")

    print(f"\nTraining Set:")
    print(f"  Positive samples: {len(stats['train_positive'])}")
    print(f"  Negative samples: {len(stats['train_negative'])}")
    print(f"  Total training: {len(stats['train_positive']) + len(stats['train_negative'])}")

    print(f"\nValidation Set:")
    print(f"  Positive samples: {len(stats['val_positive'])}")
    print(f"  Negative samples: {len(stats['val_negative'])}")
    print(f"  Total validation: {len(stats['val_positive']) + len(stats['val_negative'])}")

    if 'files_processed' in stats:
        print(f"\nFiles Processed:")
        print(f"  Train positive: {stats['files_processed']['train_positive']}")
        print(f"  Train negative: {stats['files_processed']['train_negative']}")
        print(f"  Validation positive: {stats['files_processed']['val_positive']}")
        print(f"  Validation negative: {stats['files_processed']['val_negative']}")
        print(f"  Total files processed: {stats['files_processed']['total']}")

    # Show segmentation statistics
    print(f"\nSegmentation Processing:")
    print(f"  Successfully processed: {stats['segmentation_success']}")
    print(f"  Total masks created: {stats['total_masks_created']}")
    print(f"  Segmentation errors: {len(stats['segmentation_errors'])}")

    if stats['segmentation_errors']:
        print(f"  Segmentation errors (first 5):")
        for error in stats['segmentation_errors'][:5]:
            print(f"    - {error}")
        if len(stats['segmentation_errors']) > 5:
            print(f"    ... and {len(stats['segmentation_errors']) - 5} more segmentation errors")

    # Show focus detection statistics if applicable
    if 'focus_detection_modified' in stats and stats['focus_detection_modified'] > 0:
        print(f"\nFocus Detection Mode:")
        print(f"  Label files modified (all classes -> 0): {stats['focus_detection_modified']}")

    if stats['missing_images']:
        print(f"\nMissing images (first 10):")
        for missing in stats['missing_images'][:10]:
            print(f"  - {missing}")
        if len(stats['missing_images']) > 10:
            print(f"  ... and {len(stats['missing_images']) - 10} more")

    if stats['errors']:
        print(f"\nGeneral errors encountered: {len(stats['errors'])}")
        for error in stats['errors'][:5]:
            print(f"  - {error}")
        if len(stats['errors']) > 5:
            print(f"  ... and {len(stats['errors']) - 5} more errors")

def prepare_training_data(labels_dir, image_pools_dir, output_dir,
                         validation_split=0.1, include_negative_samples=False,
                         negative_samples_percent=30, is_preprocess_image=False,
                         is_disable_highlight=False, is_detected_focus=False,
                         expand_width=False, remove_green=False,
                         use_binary_mask=False, sampling_ratio=1.0, seed=42, dry_run=True):
    """
    Prepare training data by organizing positive samples and optionally adding negative samples.

    Args:
        labels_dir (str): Directory containing .txt label files
        image_pools_dir (str): Directory containing corresponding image files
        output_dir (str): Output directory for organized training data
        validation_split (float): Percentage of data for validation (default: 0.1 = 10%)
        include_negative_samples (bool): Whether to include negative samples
        negative_samples_percent (int): Percentage of negative samples relative to positive samples
        is_preprocess_image (bool): Whether to preprocess images using preprocessing functions
        is_disable_highlight (bool): If True, disable polyline drawing in preprocessing (only when is_preprocess_image=True)
        is_detected_focus (bool): If True, convert all class labels to 0 (focus detection mode)
        expand_width (bool): If True, expand bounding boxes to the right edge of the image
        remove_green (bool): Parameter for preprocessing (if applicable)
        use_binary_mask (bool): If True, use preprocess_sonic_logging_binary_mask() instead of preprocess_sonic_logging()
        sampling_ratio (float): Ratio of data to sample (0.0-1.0). 1.0 uses all data, 0.3 uses 30% for experiments
        seed (int): Random seed for reproducible data splitting and sampling
        dry_run (bool): If True, only show what would be processed without actually moving files

    Returns:
        dict: Statistics about the training data preparation

    Directory structure:
        output_dir/
        ├── images/
        │   ├── train/              # Training images (original or preprocessed)
        │   └── validation/         # Validation images (original or preprocessed)
        └── labels/
            ├── train/              # Label files (original or modified for focus detection)
            └── validation/         # Label files (original or modified for focus detection)

    Note:
        - When is_preprocess_image=True, images are processed through preprocess_sonic_logging()
        - When is_disable_highlight=True, preprocessing skips polyline drawing
        - When is_detected_focus=True, all class IDs in labels are converted to 0
    """
    import random
    import shutil
    import cv2

    # Set random seed for reproducible results
    random.seed(seed)
    np.random.seed(seed)

    if not os.path.exists(labels_dir):
        print(f"Labels directory not found: {labels_dir}")
        return None

    if not os.path.exists(image_pools_dir):
        print(f"Image pools directory not found: {image_pools_dir}")
        return None

    # Create output directories
    train_images_dir = os.path.join(output_dir, "images", "train")
    train_labels_dir = os.path.join(output_dir, "labels", "train")
    val_images_dir = os.path.join(output_dir, "images", "validation")
    val_labels_dir = os.path.join(output_dir, "labels", "validation")

    if not dry_run:
        for dir_path in [train_images_dir, train_labels_dir, val_images_dir, val_labels_dir]:
            os.makedirs(dir_path, exist_ok=True)

    # Initialize statistics
    stats = {
        'total_label_files': 0,
        'positive_samples': [],
        'negative_samples': [],
        'train_positive': [],
        'val_positive': [],
        'train_negative': [],
        'val_negative': [],
        'missing_images': [],
        'preprocessing_errors': [],
        'preprocessing_success': 0,
        'focus_detection_modified': 0,
        'width_expansion_modified': 0,
        'errors': []
    }

    print(f"Preparing training data...")
    print(f"Labels directory: {labels_dir}")
    print(f"Image pools directory: {image_pools_dir}")
    print(f"Output directory: {output_dir}")
    print(f"Validation split: {validation_split*100:.1f}%")
    print(f"Include negative samples: {include_negative_samples}")
    if include_negative_samples:
        print(f"Negative samples percentage: {negative_samples_percent}%")
    print(f"Dry run: {dry_run}")

    # Step 1: Identify positive and negative samples
    print("\nStep 1: Identifying positive and negative samples...")
    txt_files = [f for f in os.listdir(labels_dir) if f.endswith('.txt')]
    stats['total_label_files'] = len(txt_files)

    for txt_file in tqdm(txt_files, desc="Analyzing label files"):
        label_path = os.path.join(labels_dir, txt_file)

        try:
            with open(label_path, 'r') as f:
                content = f.read().strip()

            # Find corresponding image file
            base_name = os.path.splitext(txt_file)[0]
            image_file = None

            # Look for image with common extensions
            for ext in ['.jpg', '.jpeg', '.png', '.bmp', '.tiff']:
                potential_image = base_name + ext
                image_path = os.path.join(image_pools_dir, potential_image)
                if os.path.exists(image_path):
                    image_file = potential_image
                    break

            if not image_file:
                stats['missing_images'].append(txt_file)
                continue

            # Classify as positive or negative
            if content:  # Has content (positive)
                stats['positive_samples'].append((txt_file, image_file))
            else:  # Empty (negative)
                stats['negative_samples'].append((txt_file, image_file))

        except Exception as e:
            stats['errors'].append(f"Error processing {txt_file}: {str(e)}")

    print(f"Found {len(stats['positive_samples'])} positive samples")
    print(f"Found {len(stats['negative_samples'])} negative samples")
    print(f"Missing images: {len(stats['missing_images'])}")

    # Step 2: Apply sampling if requested
    if sampling_ratio < 1.0:
        print(f"\nStep 2: Applying sampling ratio {sampling_ratio:.1%}...")

        # Sample positive samples
        original_positive_count = len(stats['positive_samples'])
        sampled_positive_count = int(original_positive_count * sampling_ratio)
        if sampled_positive_count > 0:
            random.shuffle(stats['positive_samples'])
            stats['positive_samples'] = stats['positive_samples'][:sampled_positive_count]

        # Sample negative samples if they exist
        original_negative_count = len(stats['negative_samples'])
        sampled_negative_count = int(original_negative_count * sampling_ratio)
        if sampled_negative_count > 0 and stats['negative_samples']:
            random.shuffle(stats['negative_samples'])
            stats['negative_samples'] = stats['negative_samples'][:sampled_negative_count]

        print(f"Sampled positive: {len(stats['positive_samples'])} / {original_positive_count} ({len(stats['positive_samples'])/original_positive_count*100:.1f}%)")
        print(f"Sampled negative: {len(stats['negative_samples'])} / {original_negative_count} ({len(stats['negative_samples'])/original_negative_count*100:.1f}%)")

        # Update statistics
        stats['sampling_applied'] = True
        stats['original_positive_count'] = original_positive_count
        stats['original_negative_count'] = original_negative_count
        stats['sampled_positive_count'] = len(stats['positive_samples'])
        stats['sampled_negative_count'] = len(stats['negative_samples'])
    else:
        stats['sampling_applied'] = False

    # Step 3: Split positive samples into train/validation
    print("\nStep 2: Splitting positive samples...")
    random.shuffle(stats['positive_samples'])

    val_count = int(len(stats['positive_samples']) * validation_split)
    stats['val_positive'] = stats['positive_samples'][:val_count]
    stats['train_positive'] = stats['positive_samples'][val_count:]

    print(f"Training positive samples: {len(stats['train_positive'])}")
    print(f"Validation positive samples: {len(stats['val_positive'])}")

    # Step 3: Add negative samples if requested
    if include_negative_samples and stats['negative_samples']:
        print(f"\nStep 3: Adding negative samples ({negative_samples_percent}%)...")

        # Calculate how many negative samples to add
        train_neg_count = int(len(stats['train_positive']) * negative_samples_percent / 100)
        val_neg_count = int(len(stats['val_positive']) * negative_samples_percent / 100)

        total_neg_needed = train_neg_count + val_neg_count

        if total_neg_needed > len(stats['negative_samples']):
            print(f"Warning: Need {total_neg_needed} negative samples but only {len(stats['negative_samples'])} available")
            train_neg_count = int(len(stats['negative_samples']) * len(stats['train_positive']) / len(stats['positive_samples']))
            val_neg_count = len(stats['negative_samples']) - train_neg_count

        # Randomly select negative samples
        random.shuffle(stats['negative_samples'])
        stats['train_negative'] = stats['negative_samples'][:train_neg_count]
        stats['val_negative'] = stats['negative_samples'][train_neg_count:train_neg_count + val_neg_count]

        print(f"Training negative samples: {len(stats['train_negative'])}")
        print(f"Validation negative samples: {len(stats['val_negative'])}")

    # Step 5: Copy files to training directories
    print(f"\nStep 5: Copying files to training directories...")

    def copy_files(file_pairs, dest_images_dir, dest_labels_dir, category_name):
        """Helper function to copy image and label files with optional preprocessing"""
        copied_count = 0
        for label_file, image_file in tqdm(file_pairs, desc=f"Processing {category_name}"):
            try:
                src_label = os.path.join(labels_dir, label_file)
                src_image = os.path.join(image_pools_dir, image_file)
                dest_label = os.path.join(dest_labels_dir, label_file)
                dest_image = os.path.join(dest_images_dir, image_file)

                # Handle image processing
                if is_preprocess_image:
                    # Load image as RGB
                    rgb_image = cv2.imread(src_image)
                    if rgb_image is None:
                        stats['errors'].append(f"Could not load image: {src_image}")
                        continue

                    # Convert BGR to RGB (OpenCV loads as BGR)
                    rgb_image = cv2.cvtColor(rgb_image, cv2.COLOR_BGR2RGB)

                    try:
                        # Select preprocessing function based on use_binary_mask flag
                        if use_binary_mask:
                            preprocessed_image, polyline_pts = preprocess_sonic_logging_binary_mask(rgb_image, disable_highlight=is_disable_highlight)
                            preprocess_type = "binary mask"
                        elif remove_green:
                            preprocessed_image, _ = vision_representation_featured(rgb_image, intensity_threshold=100)
                            preprocess_type = "remove green"
                        else:
                            preprocessed_image, polyline_pts = preprocess_sonic_logging(rgb_image, disable_highlight=is_disable_highlight)
                            preprocess_type = "overlay"

                        if dry_run:
                            highlight_status = "without highlights" if is_disable_highlight else "with highlights"
                            print(f"Would preprocess ({preprocess_type}, {highlight_status}) and save: {src_image} -> {dest_image}")
                        else:
                            # Handle different image formats for saving
                            if len(preprocessed_image.shape) == 3:
                                # RGB image - convert to BGR for OpenCV
                                preprocessed_bgr = cv2.cvtColor(preprocessed_image, cv2.COLOR_RGB2BGR)
                            else:
                                # Grayscale image - use as is
                                preprocessed_bgr = preprocessed_image

                            # Save preprocessed image directly to final destination
                            cv2.imwrite(dest_image, preprocessed_bgr)

                        stats['preprocessing_success'] += 1

                    except Exception as e:
                        stats['preprocessing_errors'].append(f"Preprocessing failed for {image_file}: {str(e)}")
                        # Fall back to copying original image
                        if not dry_run:
                            shutil.copy2(src_image, dest_image)
                else:
                    # No preprocessing - copy original image
                    if dry_run:
                        print(f"Would copy: {src_image} -> {dest_image}")
                    else:
                        shutil.copy2(src_image, dest_image)

                # Handle label file processing
                if is_detected_focus or expand_width:
                    # Process label file for focus detection and/or width expansion
                    modifications = []
                    if is_detected_focus:
                        modifications.append("convert all classes to 0")
                    if expand_width:
                        modifications.append("expand bounding boxes to right edge")

                    if dry_run:
                        print(f"Would process label ({', '.join(modifications)}): {src_label} -> {dest_label}")
                    else:
                        try:
                            with open(src_label, 'r') as f:
                                lines = f.readlines()

                            modified_lines = []
                            for line in lines:
                                line = line.strip()
                                if line:  # Non-empty line
                                    parts = line.split()
                                    if len(parts) >= 5:  # Valid YOLO format: class x_center y_center width height
                                        class_id = parts[0]
                                        x_center = float(parts[1])
                                        y_center = float(parts[2])
                                        width = float(parts[3])
                                        height = float(parts[4])

                                        # Apply focus detection modification
                                        if is_detected_focus:
                                            class_id = '0'

                                        # Apply width expansion modification
                                        if expand_width:
                                            # Calculate current left and right edges
                                            left_edge = x_center - (width / 2)
                                            # Expand to right edge of image (x=1.0 in normalized coordinates)
                                            right_edge = 1.0
                                            # Calculate new width and center
                                            new_width = right_edge - left_edge
                                            new_x_center = left_edge + (new_width / 2)

                                            # Update values
                                            x_center = new_x_center
                                            width = new_width

                                        # Reconstruct the line
                                        modified_line = f"{class_id} {x_center:.6f} {y_center:.6f} {width:.6f} {height:.6f}\n"
                                        modified_lines.append(modified_line)
                                    else:
                                        # Keep invalid lines as-is
                                        modified_lines.append(line + '\n')
                                else:
                                    # Keep empty lines
                                    modified_lines.append(line + '\n')

                            # Write modified label file
                            with open(dest_label, 'w') as f:
                                f.writelines(modified_lines)

                            if is_detected_focus:
                                stats['focus_detection_modified'] += 1
                            if expand_width:
                                stats['width_expansion_modified'] += 1

                        except Exception as e:
                            error_msg = f"Error processing label {label_file}"
                            if is_detected_focus and expand_width:
                                error_msg += " for focus detection and width expansion"
                            elif is_detected_focus:
                                error_msg += " for focus detection"
                            elif expand_width:
                                error_msg += " for width expansion"
                            stats['errors'].append(f"{error_msg}: {str(e)}")
                            # Fall back to copying original
                            shutil.copy2(src_label, dest_label)
                else:
                    # Copy label file unchanged
                    if dry_run:
                        print(f"Would copy: {src_label} -> {dest_label}")
                    else:
                        shutil.copy2(src_label, dest_label)

                copied_count += 1

            except Exception as e:
                stats['errors'].append(f"Error processing {category_name} {label_file}: {str(e)}")

        return copied_count

    # Copy training files
    train_pos_copied = copy_files(stats['train_positive'], train_images_dir, train_labels_dir, "train positive")
    train_neg_copied = copy_files(stats['train_negative'], train_images_dir, train_labels_dir, "train negative")

    # Copy validation files
    val_pos_copied = copy_files(stats['val_positive'], val_images_dir, val_labels_dir, "validation positive")
    val_neg_copied = copy_files(stats['val_negative'], val_images_dir, val_labels_dir, "validation negative")

    # Update statistics
    stats['files_copied'] = {
        'train_positive': train_pos_copied,
        'train_negative': train_neg_copied,
        'val_positive': val_pos_copied,
        'val_negative': val_neg_copied,
        'total': train_pos_copied + train_neg_copied + val_pos_copied + val_neg_copied
    }

    return stats

def print_training_data_statistics(stats):
    """
    Print formatted statistics about the training data preparation.

    Args:
        stats (dict): Statistics from prepare_training_data()
    """
    if not stats:
        return

    print("\n" + "=" * 70)
    print("TRAINING DATA PREPARATION STATISTICS")
    print("=" * 70)

    print(f"Total label files analyzed: {stats['total_label_files']}")

    # Show sampling information if applicable
    if stats.get('sampling_applied', False):
        print(f"Original positive samples: {stats['original_positive_count']}")
        print(f"Original negative samples: {stats['original_negative_count']}")
        print(f"Sampled positive samples: {stats['sampled_positive_count']} ({stats['sampled_positive_count']/stats['original_positive_count']*100:.1f}%)")
        print(f"Sampled negative samples: {stats['sampled_negative_count']} ({stats['sampled_negative_count']/stats['original_negative_count']*100:.1f}%)")
    else:
        print(f"Positive samples found: {len(stats['positive_samples'])}")
        print(f"Negative samples found: {len(stats['negative_samples'])}")

    print(f"Missing image files: {len(stats['missing_images'])}")

    print(f"\nTraining Set:")
    print(f"  Positive samples: {len(stats['train_positive'])}")
    print(f"  Negative samples: {len(stats['train_negative'])}")
    print(f"  Total training: {len(stats['train_positive']) + len(stats['train_negative'])}")

    print(f"\nValidation Set:")
    print(f"  Positive samples: {len(stats['val_positive'])}")
    print(f"  Negative samples: {len(stats['val_negative'])}")
    print(f"  Total validation: {len(stats['val_positive']) + len(stats['val_negative'])}")

    if 'files_copied' in stats:
        print(f"\nFiles Copied:")
        print(f"  Train positive: {stats['files_copied']['train_positive']}")
        print(f"  Train negative: {stats['files_copied']['train_negative']}")
        print(f"  Validation positive: {stats['files_copied']['val_positive']}")
        print(f"  Validation negative: {stats['files_copied']['val_negative']}")
        print(f"  Total files copied: {stats['files_copied']['total']}")

    # Show preprocessing statistics if applicable
    if 'preprocessing_success' in stats:
        print(f"\nImage Preprocessing:")
        print(f"  Successfully preprocessed: {stats['preprocessing_success']}")
        print(f"  Preprocessing errors: {len(stats['preprocessing_errors'])}")

        if stats['preprocessing_errors']:
            print(f"  Preprocessing errors (first 5):")
            for error in stats['preprocessing_errors'][:5]:
                print(f"    - {error}")
            if len(stats['preprocessing_errors']) > 5:
                print(f"    ... and {len(stats['preprocessing_errors']) - 5} more preprocessing errors")

    # Show focus detection statistics if applicable
    if 'focus_detection_modified' in stats and stats['focus_detection_modified'] > 0:
        print(f"\nFocus Detection Mode:")
        print(f"  Label files modified (all classes -> 0): {stats['focus_detection_modified']}")

    # Show width expansion statistics if applicable
    if 'width_expansion_modified' in stats and stats['width_expansion_modified'] > 0:
        print(f"\nWidth Expansion Mode:")
        print(f"  Label files modified (bounding boxes expanded to right): {stats['width_expansion_modified']}")

    if stats['missing_images']:
        print(f"\nMissing images (first 10):")
        for missing in stats['missing_images'][:10]:
            print(f"  - {missing}")
        if len(stats['missing_images']) > 10:
            print(f"  ... and {len(stats['missing_images']) - 10} more")

    if stats['errors']:
        print(f"\nGeneral errors encountered: {len(stats['errors'])}")
        for error in stats['errors'][:5]:
            print(f"  - {error}")
        if len(stats['errors']) > 5:
            print(f"  ... and {len(stats['errors']) - 5} more errors")

def remove_number_prefix_from_filenames(directory_path, file_extensions=['.jpg', '.jpeg', '.png', '.txt'], dry_run=True):
    """
    Remove number prefixes from filenames in a directory.

    Examples:
        "1_3YL13_Pile OK_0.jpg" -> "3YL13_Pile OK_0.jpg"
        "4_3YL13_Pile OK_5.txt" -> "3YL13_Pile OK_5.txt"
        "3YL13_Pile OK_0.jpg" -> "3YL13_Pile OK_0.jpg" (no change)

    Args:
        directory_path (str): Path to directory containing files to rename
        file_extensions (list): List of file extensions to process
        dry_run (bool): If True, only show what would be renamed without actually renaming

    Returns:
        dict: Statistics about the renaming operation
    """
    import re

    if not os.path.exists(directory_path):
        print(f"Directory not found: {directory_path}")
        return None

    stats = {
        'total_files_processed': 0,
        'files_renamed': 0,
        'files_skipped': 0,
        'renamed_files': [],
        'skipped_files': [],
        'errors': []
    }

    # Get all files with specified extensions using os.listdir (more reliable than glob)
    all_files = []
    try:
        all_files_in_dir = os.listdir(directory_path)

        # Filter files by extensions (case-insensitive)
        for filename in all_files_in_dir:
            file_lower = filename.lower()
            for ext in file_extensions:
                ext_lower = ext.lower()
                if file_lower.endswith(ext_lower):
                    all_files.append(filename)
                    break  # Found a match, no need to check other extensions

        # Remove duplicates while preserving order
        all_files = list(dict.fromkeys(all_files))

        print(f"DIRECTORY: {directory_path}")
        print(f"Total files in directory: {len(all_files_in_dir)}")
        print(f"Files matching extensions {file_extensions}: {len(all_files)}")
        if all_files:
            print(f"First 5 matching files: {all_files[:5]}")

    except Exception as e:
        stats['errors'].append(f"Error reading directory {directory_path}: {str(e)}")
        return stats

    if not all_files:
        print(f"No files found with extensions {file_extensions} in: {directory_path}")
        print(f"Available files (first 10): {all_files_in_dir[:10]}")
        return stats

    # Pattern to match number prefix: starts with digits followed by underscore
    number_prefix_pattern = re.compile(r'^(\d+)_(.+)$')

    for filename in tqdm(all_files, desc="Processing filenames"):
        stats['total_files_processed'] += 1

        try:
            # Check if filename has number prefix
            match = number_prefix_pattern.match(filename)

            if match:
                # Extract the rest of the filename (remove number prefix)
                rest_of_filename = match.group(2)
                new_filename = rest_of_filename

                old_path = os.path.join(directory_path, filename)
                new_path = os.path.join(directory_path, new_filename)

                # Check if new filename already exists
                if os.path.exists(new_path) and old_path != new_path:
                    stats['errors'].append(f"Cannot rename {filename} -> {new_filename}: Target file already exists")
                    stats['skipped_files'].append(filename)
                    stats['files_skipped'] += 1
                    continue

                if dry_run:
                    print(f"Would rename: {filename} -> {new_filename}")
                    stats['renamed_files'].append((filename, new_filename))
                    stats['files_renamed'] += 1
                else:
                    # Actually rename the file
                    os.rename(old_path, new_path)
                    print(f"Renamed: {filename} -> {new_filename}")
                    stats['renamed_files'].append((filename, new_filename))
                    stats['files_renamed'] += 1
            else:
                # No number prefix found, skip
                stats['skipped_files'].append(filename)
                stats['files_skipped'] += 1

        except Exception as e:
            stats['errors'].append(f"Error processing {filename}: {str(e)}")

    return stats

def batch_remove_number_prefix(image_dir, label_dir, dry_run=True):
    """
    Remove number prefixes from both image and label files in their respective directories.

    Args:
        image_dir (str): Directory containing image files
        label_dir (str): Directory containing label files
        dry_run (bool): If True, only show what would be renamed without actually renaming

    Returns:
        dict: Combined statistics from both operations
    """
    print("=" * 60)
    print("BATCH FILENAME PREFIX REMOVAL")
    print("=" * 60)

    combined_stats = {
        'image_stats': None,
        'label_stats': None,
        'total_files_processed': 0,
        'total_files_renamed': 0,
        'total_files_skipped': 0,
        'total_errors': 0
    }

    # Process image files
    if os.path.exists(image_dir):
        print(f"\nProcessing image files in: {image_dir}")
        image_stats = remove_number_prefix_from_filenames(
            image_dir,
            file_extensions=['.jpg', '.jpeg', '.png', '.bmp', '.tiff'],
            dry_run=dry_run
        )
        combined_stats['image_stats'] = image_stats

        if image_stats:
            combined_stats['total_files_processed'] += image_stats['total_files_processed']
            combined_stats['total_files_renamed'] += image_stats['files_renamed']
            combined_stats['total_files_skipped'] += image_stats['files_skipped']
            combined_stats['total_errors'] += len(image_stats['errors'])
    else:
        print(f"Image directory not found: {image_dir}")

    # Process label files
    if os.path.exists(label_dir):
        print(f"\nProcessing label files in: {label_dir}")
        label_stats = remove_number_prefix_from_filenames(
            label_dir,
            file_extensions=['.txt', '.xml', '.json'],
            dry_run=dry_run
        )
        combined_stats['label_stats'] = label_stats

        if label_stats:
            combined_stats['total_files_processed'] += label_stats['total_files_processed']
            combined_stats['total_files_renamed'] += label_stats['files_renamed']
            combined_stats['total_files_skipped'] += label_stats['files_skipped']
            combined_stats['total_errors'] += len(label_stats['errors'])
    else:
        print(f"Label directory not found: {label_dir}")

    return combined_stats

def print_rename_statistics(stats):
    """
    Print formatted statistics about the renaming operation.

    Args:
        stats (dict): Statistics from remove_number_prefix_from_filenames or batch_remove_number_prefix
    """
    if 'image_stats' in stats and 'label_stats' in stats:
        # This is from batch operation
        print("\n" + "=" * 50)
        print("BATCH RENAME SUMMARY")
        print("=" * 50)
        print(f"Total files processed: {stats['total_files_processed']}")
        print(f"Total files renamed: {stats['total_files_renamed']}")
        print(f"Total files skipped: {stats['total_files_skipped']}")
        print(f"Total errors: {stats['total_errors']}")

        if stats['image_stats']:
            print(f"\nImage files: {stats['image_stats']['files_renamed']} renamed, {stats['image_stats']['files_skipped']} skipped")
        if stats['label_stats']:
            print(f"Label files: {stats['label_stats']['files_renamed']} renamed, {stats['label_stats']['files_skipped']} skipped")

    else:
        # This is from single directory operation
        print("\n" + "=" * 50)
        print("RENAME STATISTICS")
        print("=" * 50)
        print(f"Total files processed: {stats['total_files_processed']}")
        print(f"Files renamed: {stats['files_renamed']}")
        print(f"Files skipped (no prefix): {stats['files_skipped']}")
        print(f"Errors: {len(stats['errors'])}")

        if stats['renamed_files']:
            print(f"\nFirst 10 renamed files:")
            for old_name, new_name in stats['renamed_files'][:10]:
                print(f"  {old_name} -> {new_name}")
            if len(stats['renamed_files']) > 10:
                print(f"  ... and {len(stats['renamed_files']) - 10} more files")

        if stats['errors']:
            print(f"\nErrors encountered:")
            for error in stats['errors'][:5]:
                print(f"  - {error}")
            if len(stats['errors']) > 5:
                print(f"  ... and {len(stats['errors']) - 5} more errors")
# Additional utility functions for filename management
def demo_filename_prefix_removal():
    """
    Demonstration of filename prefix removal functions.
    """
    print("\n" + "=" * 60)
    print("FILENAME PREFIX REMOVAL DEMO")
    print("=" * 60)

    # Example directories (adjust these paths to your actual directories)
    image_directory = "../data/process_data/for_label/fold_1_03052025/full"
    label_directory = "../data/process_data/[SNL]_data/labels_pool"

    print("Example usage:")
    print("1. Single directory processing:")
    print(f"   remove_number_prefix_from_filenames('{image_directory}', dry_run=True)")
    print("\n2. Batch processing (images + labels):")
    print(f"   batch_remove_number_prefix('{image_directory}', '{label_directory}', dry_run=True)")

    print("\nExample transformations:")
    examples = [
        "1_3YL13_Pile OK_0.jpg -> 3YL13_Pile OK_0.jpg",
        "4_3YL13_Pile OK_5.txt -> 3YL13_Pile OK_5.txt",
        "123_sample_image.png -> sample_image.png",
        "3YL13_Pile OK_0.jpg -> 3YL13_Pile OK_0.jpg (no change - no prefix)"
    ]

    for example in examples:
        print(f"  • {example}")

    print("\nTo actually rename files, set dry_run=False")

    # Uncomment the lines below to run the actual operations:
    # print("\nRunning dry run on image directory...")
    # stats = remove_number_prefix_from_filenames(image_directory, dry_run=True)
    # if stats:
    #     print_rename_statistics(stats)

    # print("\nRunning batch dry run...")
    # batch_stats = batch_remove_number_prefix(image_directory, label_directory, dry_run=True)
    # print_rename_statistics(batch_stats)

def demo_defect_d_filtering():
    """
    Demonstration of defect_D filtering functions.
    """
    print("\n" + "=" * 60)
    print("DEFECT_D FILTERING DEMO")
    print("=" * 60)

    # Example directories (adjust these paths to your actual directories)
    labels_directory = "../data/process_data/[SNL]_data/labels_pool"

    print("Example usage:")
    print("1. Single folder processing:")
    print(f"   filter_top_defect_d_labels('{labels_directory}', 'fold_1', top_percentage=20, dry_run=True)")

    print("\n2. Batch processing multiple folders:")
    print("   training_folders = ['fold_1', 'fold_2', 'fold_3']")
    print(f"   batch_filter_defect_d_for_training_folders('{labels_directory}', training_folders, dry_run=True)")

    print("\nWhat this function does:")
    print("• Reads each .txt label file in YOLO format")
    print("• Identifies defect_D annotations (class 2)")
    print("• Removes defect_D annotations located in top 20% of image (y_center <= 0.20)")
    print("• Saves filtered labels to: ../data/process_data/[SNL]_data/for_training/<folder_name>/labels_no_top_D/")
    print("• Provides detailed statistics about removed annotations")

    print("\nExample YOLO format:")
    print("  2 0.5 0.1 0.2 0.1  <- defect_D in top area (y_center=0.1) - REMOVED")
    print("  2 0.5 0.8 0.2 0.1  <- defect_D in bottom area (y_center=0.8) - KEPT")
    print("  0 0.3 0.1 0.1 0.1  <- defect_B in top area (y_center=0.1) - KEPT")

    print("\nTo actually process files, set dry_run=False")

    # Uncomment the lines below to run the actual operations:
    # print("\nRunning single folder dry run...")
    # stats = filter_top_defect_d_labels(labels_directory, "test_folder", dry_run=True)
    # if stats:
    #     print_filter_statistics(stats)

    # print("\nRunning batch dry run...")
    # training_folders = ['fold_1', 'fold_2', 'fold_3']
    # batch_stats = batch_filter_defect_d_for_training_folders(labels_directory, training_folders, dry_run=True)

def analyze_labels_in_directory(labels_dir, directory_name=""):
    """
    Comprehensive analysis of label files in a specific directory.

    Args:
        labels_dir (str): Path to directory containing .txt label files
        directory_name (str): Optional name for display purposes

    Returns:
        dict: Combined statistics from both positive/negative and class analysis
    """
    print(f"\n{'='*70}")
    print(f"ANALYZING LABELS IN: {directory_name if directory_name else labels_dir}")
    print(f"{'='*70}")

    if not os.path.exists(labels_dir):
        print(f"Directory not found: {labels_dir}")
        return None

    results = {
        'directory': labels_dir,
        'directory_name': directory_name,
        'sample_stats': None,
        'class_stats': None
    }

    # Step 1: Check for positive and negative samples
    print("\n1. Analyzing positive and negative samples...")
    sample_stats = check_positive_negative_samples(labels_dir)
    results['sample_stats'] = sample_stats

    if sample_stats:
        print_sample_statistics(sample_stats)

        # Plot positive/negative distribution
        print("\n2. Plotting positive/negative sample distribution...")
        plot_positive_negative_distribution(sample_stats)

    # Step 2: Analyze class instances
    print("\n3. Analyzing class instances...")
    class_stats = analyze_class_instances(labels_dir)
    results['class_stats'] = class_stats

    if class_stats:
        print_class_statistics(class_stats)

        # Plot class distribution
        print("\n4. Plotting class distribution...")
        plot_class_distribution(class_stats)

    return results

def compare_label_directories(directories_info):
    """
    Compare multiple label directories side by side.

    Args:
        directories_info (list): List of tuples (directory_path, display_name)

    Example:
        directories_info = [
            ("../data/original_labels/", "Original Labels"),
            ("../data/filtered_labels/", "Filtered Labels"),
            ("../data/training_labels/", "Training Labels")
        ]
    """
    print(f"\n{'='*80}")
    print("COMPARING MULTIPLE LABEL DIRECTORIES")
    print(f"{'='*80}")

    all_results = []

    for labels_dir, display_name in directories_info:
        results = analyze_labels_in_directory(labels_dir, display_name)
        if results:
            all_results.append(results)

    # Print comparison summary
    if len(all_results) > 1:
        print(f"\n{'='*80}")
        print("COMPARISON SUMMARY")
        print(f"{'='*80}")

        print(f"{'Directory':<30} {'Total Files':<12} {'Positive':<10} {'Negative':<10} {'Annotations':<12}")
        print("-" * 80)

        for result in all_results:
            name = result['directory_name'] or os.path.basename(result['directory'])
            sample_stats = result['sample_stats']
            class_stats = result['class_stats']

            if sample_stats and class_stats:
                print(f"{name:<30} {sample_stats['total_files']:<12} "
                      f"{sample_stats['positive_count']:<10} {sample_stats['negative_count']:<10} "
                      f"{class_stats['total_annotations']:<12}")

    return all_results

# IMAGE PROCESSING MODULE

def extract_density_validated_peaks(
    binary_mask: np.ndarray,
    skip_left_percent: float = 0.1,
    window_width: int = 20,
    window_half_height: int = 1,
    density_threshold: float = 0.8
) -> List[Tuple[int, int]]:
    """
    Extract (y, x) peaks from binary mask using local density validation.

    Args:
        binary_mask (np.ndarray): Binary image (H, W) where 255 = signal, 0 = background.
        skip_left_percent (float): Portion of image width to skip from the left (axis area).
        window_width (int): Width of window to the right of the peak to validate density.
        window_half_height (int): Number of rows above and below to include in the window.
        density_threshold (float): Minimum % of white pixels to consider a peak valid.

    Returns:
        List[Tuple[int, int]]: List of valid (y, x) peak positions.
    """
    height, width = binary_mask.shape
    skip_x = int(width * skip_left_percent)
    peaks = []

    for y in range(height):
        row = binary_mask[y, skip_x:]
        white_indices = np.where(row == 255)[0]
        if white_indices.size == 0:
            continue

        # Candidate peak x (shifted back to full image coords)
        x = white_indices[0] + skip_x

        # Define window boundaries
        y_start = max(0, y - window_half_height)
        y_end = min(height, y + window_half_height + 1)
        x_start = x
        x_end = min(width, x + window_width)

        window = binary_mask[y_start:y_end, x_start:x_end]
        white_pixel_count = np.count_nonzero(window == 255)
        total_pixel_count = window.size
        density = white_pixel_count / total_pixel_count

        if density >= density_threshold:
            peaks.append((y, x))

    return peaks



def extract_first_peak_vertices(binary_masks: np.ndarray)-> np.ndarray:
    """
    """
    H, W = binary_masks.shape
    peak_coords = []

    for y in range(H):
        row = binary_masks[y, :]
        white_indices = np.where(row == 255)[0]
        if len(white_indices) > 0:
            x = white_indices[0]  # first white pixel from left
            peak_coords.append((x, y))  # (x, y)

    return np.array(peak_coords)

def extract_first_peak_by_row(
    binary_mask: np.ndarray,
    skip_left_percent: float = 0.1
) -> List[Tuple[int, int]]:
    """
    Extract the first white pixel (255) in each row of a binary image.

    Args:
        binary_mask (np.ndarray): Binary image with white = signal (255), shape (H, W).
        skip_left_percent (float): Percentage of width to skip from the left (to ignore axes).

    Returns:
        List[Tuple[int, int]]: List of (y, x) coordinates of first white pixel per row.
    """
    height, width = binary_mask.shape
    skip_x = int(width * skip_left_percent)
    peaks = []

    for y in range(height):
        row = binary_mask[y, skip_x:]
        white_indices = np.where(row == 255)[0]
        if white_indices.size > 0:
            x = white_indices[0] + skip_x
            peaks.append((y, x))

    return peaks


def extract_signal_mask(image_path: str, threshold_ratio: float = 0.4) -> np.ndarray:
    """
    Convert waveform image to binary mask where signal regions are white.

    Args:
        image_path (str): Path to input image.
        threshold_ratio (float): Threshold ratio (0–1) for binarization.

    Returns:
        np.ndarray: Binary mask (uint8), white = signal.
    """
    # Load image in color and convert to grayscale
    image = cv2.imread(image_path)
    if image is None:
        raise FileNotFoundError(f"Failed to load: {image_path}")
    
    gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)

    # Normalize to [0, 255] just in case
    gray = cv2.normalize(gray, None, 0, 255, cv2.NORM_MINMAX)

    # Threshold: binary inverse to highlight bright areas
    _, binary_mask = cv2.threshold(
        gray, int(threshold_ratio * 255), 255, cv2.THRESH_BINARY
    )

    return binary_mask

def extract_first_column_peaks(
    binary_mask: np.ndarray,
    skip_left_percent: float = 0.1,
    min_run_length: int = 5
) -> List[Tuple[int, int]]:
    height, width = binary_mask.shape
    skip_x = int(width * skip_left_percent)
    peaks = []

    for y in range(height):
        row = binary_mask[y, skip_x:]
        white_indices = np.where(row == 255)[0]
        if white_indices.size == 0:
            continue

        runs = np.split(white_indices, np.where(np.diff(white_indices) != 1)[0] + 1)
        for run in runs:
            if len(run) >= min_run_length:
                x = run[0] + skip_x
                peaks.append((y, x))
                break

    return peaks

def filter_leftward_outliers(
    peaks: List[Tuple[int, int]],
    window: int = 5,
    max_left_offset: int = 20
) -> List[Tuple[int, int]]:
    """
    Remove (y, x) peaks that are far to the LEFT of their vertical neighbors.

    Args:
        peaks (List[Tuple[int, int]]): List of (y, x) peaks (ideally sorted by y).
        window (int): Vertical window size (above/below) to check neighboring x positions.
        max_left_offset (int): Threshold for left deviation — peaks that are too far left are removed.

    Returns:
        List[Tuple[int, int]]: Filtered peaks with leftward outliers removed.
    """
    if not peaks:
        return []

    # Convert to dictionary for quick lookup: y -> x
    peaks_dict = dict(peaks)
    valid_peaks = []

    all_ys = sorted(peaks_dict.keys())

    for y in all_ys:
        x = peaks_dict[y]
        neighbor_xs = []

        for dy in range(-window, window + 1):
            ny = y + dy
            if ny in peaks_dict and ny != y:
                neighbor_xs.append(peaks_dict[ny])

        if not neighbor_xs:
            continue

        median_x = np.median(neighbor_xs)

        # ✅ Main rule: if this point is NOT too far to the left, keep it
        if x >= median_x - max_left_offset:
            valid_peaks.append((y, x))

    return valid_peaks



import random
import cv2

def filter_peaks_by_continuity(
    peaks: List[Tuple[int, int]],
    max_x_jump: int = 15
) -> List[Tuple[int, int]]:
    """
    Filter peaks to remove those with large x-coordinate jumps between rows.

    Args:
        peaks (List[Tuple[int, int]]): List of (y, x) peaks sorted by y.
        max_x_jump (int): Maximum allowed horizontal gap (in pixels) between consecutive peaks.

    Returns:
        List[Tuple[int, int]]: Filtered peaks.
    """
    if not peaks:
        return []

    filtered = [peaks[0]]
    for i in range(1, len(peaks)):
        y, x = peaks[i]
        y_prev, x_prev = filtered[-1]
        if abs(x - x_prev) <= max_x_jump:
            filtered.append((y, x))

    return filtered

def plot_signal_peak_triplets(folder_path: str, num_samples: int = 3):
    """
    Plot grayscale, binary mask, and peak overlay for randomly selected images.

    Args:
        folder_path (str): Path to image folder.
        num_samples (int): Number of random samples to plot.
    """
    image_names = os.listdir(folder_path)
    selected_images = random.sample(image_names, min(num_samples, len(image_names)))

    fig, axes = plt.subplots(num_samples, 3, figsize=(15, 5 * num_samples))

    if num_samples == 1:
        axes = [axes]  # Ensure iterable

    for i, image_name in enumerate(selected_images):
        filename = os.path.join(folder_path, image_name)
        image = cv2.imread(filename)
        rgb_image = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)

        gray_image = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        gray_blurred = cv2.GaussianBlur(gray_image, (5, 5), 0)
        _, binary_mask = cv2.threshold(gray_blurred, 160, 255, cv2.THRESH_BINARY)
        binary_mask = 255 - binary_mask  # Invert to make signal white
        kernel = np.ones((3, 3), np.uint8)
        binary_mask = cv2.morphologyEx(binary_mask, cv2.MORPH_OPEN, kernel)

        overlay_img = preprocess_sonic_logging(rgb_image, disable_highlight=True)

        # Plot each column
        axs = axes[i] if num_samples > 1 else axes

        axs[0].imshow(gray_image, cmap='gray')
        axs[0].set_title(f"Grayscale: {image_name}")
        axs[0].axis("off")

        axs[1].imshow(binary_mask, cmap='gray')
        axs[1].set_title("Inverted Binary Mask")
        axs[1].axis("off")

        axs[2].imshow(overlay_img)
        axs[2].set_title("First Peaks Marked")
        axs[2].axis("off")

    plt.tight_layout()
    plt.show()


def preprocess_sonic_logging(rgb_image, disable_highlight=False, draw_highlighting=False):
    """
    Preprocess sonic logging image with optional polyline highlighting.

    Args:
        rgb_image: Input RGB image
        disable_highlight (bool): If True, skip polyline drawing

    Returns:
        Processed image (RGB format)
    """
    gray_image = cv2.cvtColor(rgb_image, cv2.COLOR_RGB2GRAY)
    # Smooth and threshold
    gray_blurred = cv2.GaussianBlur(gray_image, (5, 5), 0)
    _, binary_mask = cv2.threshold(gray_blurred, 160, 255, cv2.THRESH_BINARY)
    binary_mask = 255 - binary_mask  # Invert to make signal white

    # Optional: morphological denoise
    kernel = np.ones((3, 3), np.uint8)
    binary_mask = cv2.morphologyEx(binary_mask, cv2.MORPH_OPEN, kernel)

    # Create overlay image
    overlay_img = np.stack([binary_mask] * 3, axis=-1)

    # Draw polylines only if highlighting is not disabled
    if not disable_highlight:
        peaks = extract_first_column_peaks(binary_mask)
        peaks = extract_density_validated_peaks(binary_mask)
        peaks = filter_leftward_outliers(peaks)
        polyline_pts = np.array([[x, y] for y, x in peaks], dtype=np.int32)
        if len(peaks) > 1 and draw_highlighting:
            cv2.polylines(overlay_img, [polyline_pts], isClosed=False, color=(0, 255, 0), thickness=5)

    return overlay_img, polyline_pts


def preprocess_sonic_logging_binary_mask(rgb_image, disable_highlight=False):
    """
    Preprocess sonic logging image returning binary mask with polygon fill.

    Args:
        rgb_image: Input RGB image
        disable_highlight (bool): If True, disable polyline drawing (affects polygon creation)

    Returns :
        tuple: (processed_image, polyline_pts)
            - processed_image: Binary mask with polygon fill (grayscale)
            - polyline_pts: Array of polyline points or None if no peaks found
    """
    overlay_image, peaks_xy = preprocess_sonic_logging(rgb_image, disable_highlight=disable_highlight)

    if peaks_xy is None or len(peaks_xy) == 0:
        # If no peaks found, return a black mask
        h, w = overlay_image.shape[:2]
        dark_frame = np.zeros((h, w), dtype=np.uint8)
        return dark_frame, None

    # 1. Invert and extract grayscale
    inverted_overlay = 255 - overlay_image
    h, w = inverted_overlay.shape[:2]

    # 2. Append bottom-right and top-right corner to close polygon
    peaks_xy_extended = np.append(peaks_xy, [[w - 10, h - 1], [w - 10, 0]], axis=0)
    polygon_array = np.array(peaks_xy_extended, dtype=np.int32).reshape((-1, 1, 2))

    # 3. Create dark frame and fill the white polygon
    dark_frame = np.zeros_like(inverted_overlay[:, :, 0] if len(inverted_overlay.shape) == 3 else inverted_overlay)
    cv2.fillPoly(dark_frame, [polygon_array], color=255, lineType=cv2.LINE_8)

    return dark_frame, peaks_xy


# ====== SEGMENTATION MASK PREPARATION ==========

def extract_rois_from_yolo_labels(image: np.ndarray, label_path: str):
    """
    Extract regions of interest (ROIs) from an image based on YOLO format label file.
    Returns:
        List[Tuple[int, int, int, int]], List[np.ndarray]: 
            A list of (x, y, w, h) bounding box coordinates (in pixel),
            and corresponding cropped image regions (ROIs).
    """
    height, width = image.shape[:2]
    rois = []
    boxes = []
    classes = []
    with open(label_path, 'r') as f:
        lines = f.readlines()
    for line in lines:
        parts = line.strip().split()
        if len(parts) != 5:
            continue  # skip invalid lines

        cls_id, x_c, y_c, w, h = map(float, parts)
        # Convert normalized to pixel values
        x_c *= width
        y_c *= height
        w *= width
        h *= height

        x1 = int(max(x_c - w / 2, 0))
        y1 = int(max(y_c - h / 2, 0))
        x2 = int(min(x_c + w / 2, width))
        y2 = int(min(y_c + h / 2, height))

        roi = image[y1:y2, x1:x2]
        rois.append(roi)
        boxes.append((x1, y1, x2 - x1, y2 - y1))
        classes.append(cls_id)

    return boxes, rois, classes

def extract_near_black_pixels(rgb_image: np.ndarray, max_black_threshold: int = 150, skip_left_percent: float = 0.1) -> List[Tuple[int, int]]:
    """
    Extract all pixels that are nearly black (intensity close to 0) from an RGB rgb_image,
    skipping the leftmost portion of the rgb_image (e.g., axis labels).
    Returns:
        List[Tuple[int, int]]: List of (y, x) coordinates where grayscale intensity <= threshold.
    """
    gray = cv2.cvtColor(rgb_image, cv2.COLOR_RGB2GRAY)
    height, width = gray.shape
    skip_x = int(width * skip_left_percent)
    black_mask = (gray <= max_black_threshold)
    black_mask[:, :skip_x] = 0  # Zero-out the left 10%
    y_coords, x_coords = np.where(black_mask)
    peaks = list(zip(y_coords, x_coords))

    return peaks

def vision_representation_featured(rgb_image: np.ndarray, intensity_threshold: int = 120) -> Tuple[np.ndarray, List[Tuple[int, int]]]:
    """
    Extract the foreground (peak) pixels from an RGB image based on near-black pixel detection.
    Pixels not part of the peaks are set to white.
    """
    peaks = extract_near_black_pixels(rgb_image, max_black_threshold=intensity_threshold)
    foreground_image = np.ones_like(rgb_image, dtype=np.uint8) * 255
    for y, x in peaks:
        foreground_image[y, x] = rgb_image[y, x]

    return foreground_image, peaks

def create_segmentation_masks(rois: List[np.ndarray],
                               classes: List[int],
                               boxes: List[Tuple[int, int, int, int]],
                               fatness_factor: float = 0.8,
                               spike_position: float = 0.1,
                               tip_height_ratio: float = 0.2
                               ) -> Tuple[List[np.ndarray], List[List[Tuple[int, int]]]]:
    """
    Generate segmentation masks and polygon coordinates.

    For h >= 50, build polygon from first white pixel per row, and explicitly close the loop.
    """
    masks = []
    abs_positions = []

    for roi, cls, (x0, y0, w, h) in zip(rois, classes, boxes):
        mask = np.zeros((h, w), dtype=np.uint8)

        if h < 50 and cls != 2:
            # --- Spike Mask ---
            base_start = int(h * spike_position)
            base_end = int(h * (1 - spike_position))
            tip_height = max(1, int(h * tip_height_ratio))
            tip_start = (base_start + base_end) // 2 - tip_height // 2
            tip_end = tip_start + tip_height

            spike_pts = []
            for y in range(base_start, base_end + 1):
                norm = 1 - abs(y - (base_start + base_end) / 2) / ((base_end - base_start) / 2)
                line_width = int(norm * w * fatness_factor)
                if tip_start <= y <= tip_end:
                    line_width = w

                for x in range(line_width):
                    mask[y, x] = 255
                    spike_pts.append((x0 + x, y0 + y))

            masks.append(mask)
            abs_positions.append(spike_pts)

        else:
            # --- Polygon Mask ---
            lap = cv2.Laplacian(roi, cv2.CV_64F)
            lap = np.uint8(np.absolute(lap))
            _, binary = cv2.threshold(lap, 30, 255, cv2.THRESH_BINARY)

            # Left edge from first white per row
            left_edge = []
            left_edge.append((0, 0))
            for y in range(h):
                row = binary[y]
                white_indices = np.where(row > 0)[0]
                x = white_indices[0] if len(white_indices) > 0 else w - 1
                left_edge.append((x, y))
            if len(left_edge) > 2:
                polygon = left_edge.copy()
                polygon.append((0, h-1)) # Bottom left corner
                polygon.append(left_edge[0])    # back to first point

                polygon_np = np.array(polygon, dtype=np.int32)
                cv2.fillPoly(mask, [polygon_np], 255)
                kernel = np.ones((1, 7), np.uint8)
                mask = cv2.dilate(mask, kernel, iterations=1)

                # Convert to original image coordinates
                abs_xy = [(x0 + x, y0 + y) for (x, y) in polygon]
            else:
                abs_xy = []

            masks.append(mask)
            abs_positions.append(abs_xy)

    return masks, abs_positions

def overlay_polygons_on_image(image: np.ndarray,
                               polygons: List[List[Tuple[int, int]]],
                               color=(0, 0, 255),
                               alpha=0.5) -> np.ndarray:
    """
    Overlay all polygonal masks on the image in a single pass to avoid darkened overlays.
    """
    overlay_mask = np.zeros_like(image, dtype=np.uint8)

    for polygon_points in polygons:
        if len(polygon_points) < 3:
            continue
        pts = np.array(polygon_points, dtype=np.int32).reshape((-1, 1, 2))
        cv2.fillPoly(overlay_mask, [pts], color)

    # Blend once with the original image
    blended = cv2.addWeighted(overlay_mask, alpha, image, 1 - alpha, 0)

    return blended


# MAIN FUNCTION
def prepare_yolo_masks(rgb_image, label_path, is_return_image=False):
    process_frame = rgb_image.copy()
    vision_representation_bw, _ = vision_representation_featured(process_frame, intensity_threshold=150)
    boxes, rois, classes = extract_rois_from_yolo_labels(vision_representation_bw, label_path)
    label_masks, absolute_positions = create_segmentation_masks(rois, classes, boxes, fatness_factor=0.6, spike_position=0.15)

    if is_return_image:
        overlay_image = overlay_polygons_on_image(rgb_image, absolute_positions, color=(255,0 ,0), alpha=0.7)
        return absolute_positions, overlay_image
    return absolute_positions



def demo_training_data_preparation_with_preprocessing():
    """
    Demonstration of training data preparation functions with preprocessing capabilities.
    """
    print("\n" + "=" * 70)
    print("TRAINING DATA PREPARATION WITH PREPROCESSING DEMO")
    print("=" * 70)

    # Example directories (adjust these paths to your actual directories)
    labels_directory = "../data/process_data/[SNL]_data/labels_pool"
    image_pools_directory = "../data/process_data/for_label/fold_1_03052025/full"
    output_directory = "../data/process_data/[SNL]_data/training_prepared"

    print("Example usage:")
    print("1. Basic training data preparation (no preprocessing):")
    print(f"   prepare_training_data(")
    print(f"       labels_dir='{labels_directory}',")
    print(f"       image_pools_dir='{image_pools_directory}',")
    print(f"       output_dir='{output_directory}',")
    print(f"       validation_split=0.1,")
    print(f"       include_negative_samples=False,")
    print(f"       is_preprocess_image=False,")
    print(f"       dry_run=True")
    print(f"   )")

    print("\n2. Training data with negative samples and preprocessing:")
    print(f"   prepare_training_data(")
    print(f"       labels_dir='{labels_directory}',")
    print(f"       image_pools_dir='{image_pools_directory}',")
    print(f"       output_dir='{output_directory}',")
    print(f"       validation_split=0.1,")
    print(f"       include_negative_samples=True,")
    print(f"       negative_samples_percent=30,")
    print(f"       is_preprocess_image=True,")
    print(f"       dry_run=True")
    print(f"   )")

    print("\n3. Training data with preprocessing but no highlights:")
    print(f"   prepare_training_data(")
    print(f"       labels_dir='{labels_directory}',")
    print(f"       image_pools_dir='{image_pools_directory}',")
    print(f"       output_dir='{output_directory}',")
    print(f"       is_preprocess_image=True,")
    print(f"       is_disable_highlight=True,")
    print(f"       dry_run=True")
    print(f"   )")

    print("\n4. Training data for focus detection (all classes -> 0):")
    print(f"   prepare_training_data(")
    print(f"       labels_dir='{labels_directory}',")
    print(f"       image_pools_dir='{image_pools_directory}',")
    print(f"       output_dir='{output_directory}',")
    print(f"       is_preprocess_image=True,")
    print(f"       is_detected_focus=True,")
    print(f"       dry_run=True")
    print(f"   )")

    print("\n5. Training data with binary mask preprocessing:")
    print(f"   prepare_training_data(")
    print(f"       labels_dir='{labels_directory}',")
    print(f"       image_pools_dir='{image_pools_directory}',")
    print(f"       output_dir='{output_directory}',")
    print(f"       is_preprocess_image=True,")
    print(f"       use_binary_mask=True,")
    print(f"       dry_run=True")
    print(f"   )")

    print("\n6. Training data with binary mask and focus detection:")
    print(f"   prepare_training_data(")
    print(f"       labels_dir='{labels_directory}',")
    print(f"       image_pools_dir='{image_pools_directory}',")
    print(f"       output_dir='{output_directory}',")
    print(f"       is_preprocess_image=True,")
    print(f"       use_binary_mask=True,")
    print(f"       is_detected_focus=True,")
    print(f"       is_disable_highlight=True,")
    print(f"       dry_run=True")
    print(f"   )")

    print("\n7. Training data with width expansion:")
    print(f"   prepare_training_data(")
    print(f"       labels_dir='{labels_directory}',")
    print(f"       image_pools_dir='{image_pools_directory}',")
    print(f"       output_dir='{output_directory}',")
    print(f"       expand_width=True,")
    print(f"       seed=123,")
    print(f"       dry_run=True")
    print(f"   )")

    print("\n8. Training data with sampling for experiments:")
    print(f"   prepare_training_data(")
    print(f"       labels_dir='{labels_directory}',")
    print(f"       image_pools_dir='{image_pools_directory}',")
    print(f"       output_dir='{output_directory}',")
    print(f"       sampling_ratio=0.3,")
    print(f"       include_negative_samples=True,")
    print(f"       negative_samples_percent=20,")
    print(f"       seed=789,")
    print(f"       dry_run=True")
    print(f"   )")

    print("\n9. Training data with all modifications:")
    print(f"   prepare_training_data(")
    print(f"       labels_dir='{labels_directory}',")
    print(f"       image_pools_dir='{image_pools_directory}',")
    print(f"       output_dir='{output_directory}',")
    print(f"       is_preprocess_image=True,")
    print(f"       use_binary_mask=True,")
    print(f"       is_detected_focus=True,")
    print(f"       expand_width=True,")
    print(f"       include_negative_samples=True,")
    print(f"       negative_samples_percent=25,")
    print(f"       sampling_ratio=0.5,")
    print(f"       seed=456,")
    print(f"       dry_run=True")
    print(f"   )")

    print("\nWhat preprocessing does:")
    print("• Loads each image as RGB")
    print("• Applies preprocessing function based on use_binary_mask flag:")
    print("  ")
    print("  preprocess_sonic_logging() (use_binary_mask=False):")
    print("    - Converts to grayscale")
    print("    - Applies Gaussian blur and thresholding")
    print("    - Inverts binary mask (signal becomes white)")
    print("    - Applies morphological denoising")
    print("    - Extracts first column peaks")
    print("    - Creates RGB overlay with green polylines (unless is_disable_highlight=True)")
    print("  ")
    print("  preprocess_sonic_logging_binary_mask() (use_binary_mask=True):")
    print("    - Performs same initial processing as above")
    print("    - Creates polygon from peaks and corner points")
    print("    - Returns binary mask with filled polygon (grayscale)")
    print("    - Polylines affect polygon creation (unless is_disable_highlight=True)")
    print("  ")
    print("• Saves preprocessed images directly to final training directories")
    print("• Processes label files based on flags:")
    print("  - Normal mode: copies original label files unchanged")
    print("  - Focus detection mode (is_detected_focus=True): converts all class IDs to 0")
    print("  - Width expansion mode (expand_width=True): expands bounding boxes to right edge")
    print("  - Both modes can be combined for comprehensive label modification")
    print("• Uses random seed for reproducible data splitting and negative sampling")
    print("• Supports sampling_ratio for experimental training with subset of data")
    print("  - sampling_ratio=1.0: uses all available data (default)")
    print("  - sampling_ratio=0.3: uses 30% of data for quick experiments")
    print("  - sampling_ratio=0.1: uses 10% of data for rapid prototyping")

    print("\nDirectory structure:")
    print(f"  {output_directory}/")
    print("  ├── images/")
    print("  │   ├── train/              # Training images (preprocessed if flag is set)")
    print("  │   └── validation/         # Validation images (preprocessed if flag is set)")
    print("  └── labels/")
    print("      ├── train/              # Original label files")
    print("      └── validation/         # Original label files")

    print("\nError handling:")
    print("• If preprocessing fails for an image, falls back to copying original")
    print("• Detailed error reporting for preprocessing failures")
    print("• Separate tracking of preprocessing success/failure rates")

    print("\nTo actually prepare training data, set dry_run=False")

def demo_segmentation_data_preparation():
    """
    Demonstration of segmentation data preparation functions.
    """
    print("\n" + "=" * 70)
    print("SEGMENTATION DATA PREPARATION DEMO")
    print("=" * 70)

    # Example directories (adjust these paths to your actual directories)
    labels_directory = "../data/process_data/[SNL]_data/labels_pool"
    image_pools_directory = "../data/process_data/for_label/fold_1_03052025/full"
    output_directory = "../data/process_data/[SNL]_data/segmentation_prepared"

    print("Example usage:")
    print("1. Basic segmentation data preparation:")
    print(f"   prepare_segmentation_data(")
    print(f"       labels_dir='{labels_directory}',")
    print(f"       image_pools_dir='{image_pools_directory}',")
    print(f"       output_dir='{output_directory}',")
    print(f"       validation_split=0.1,")
    print(f"       include_negative_samples=False,")
    print(f"       seed=42,")
    print(f"       dry_run=True")
    print(f"   )")

    print("\n2. Segmentation data with negative samples:")
    print(f"   prepare_segmentation_data(")
    print(f"       labels_dir='{labels_directory}',")
    print(f"       image_pools_dir='{image_pools_directory}',")
    print(f"       output_dir='{output_directory}',")
    print(f"       validation_split=0.1,")
    print(f"       include_negative_samples=True,")
    print(f"       negative_samples_percent=30,")
    print(f"       seed=42,")
    print(f"       dry_run=True")
    print(f"   )")

    print("\n3. Segmentation data with focus detection:")
    print(f"   prepare_segmentation_data(")
    print(f"       labels_dir='{labels_directory}',")
    print(f"       image_pools_dir='{image_pools_directory}',")
    print(f"       output_dir='{output_directory}',")
    print(f"       is_detected_focus=True,")
    print(f"       seed=123,")
    print(f"       dry_run=True")
    print(f"   )")

    print("\n4. Segmentation data with sampling for experiments:")
    print(f"   prepare_segmentation_data(")
    print(f"       labels_dir='{labels_directory}',")
    print(f"       image_pools_dir='{image_pools_directory}',")
    print(f"       output_dir='{output_directory}',")
    print(f"       sampling_ratio=0.3,")
    print(f"       include_negative_samples=True,")
    print(f"       negative_samples_percent=30,")
    print(f"       seed=999,")
    print(f"       dry_run=True")
    print(f"   )")

    print("\nWhat this function does:")
    print("• Reads bounding box label files in YOLO format")
    print("• Loads corresponding images for processing")
    print("• Uses prepare_yolo_masks() to convert bounding boxes to segmentation masks")
    print("• Creates YOLO segmentation format labels: class x1 y1 x2 y2 ... xn yn")
    print("• Maintains same number of annotations as original bounding box labels")
    print("• Preserves class IDs from original labels (unless focus detection is enabled)")
    print("• Splits data into train/validation sets with optional negative sampling")
    print("• Provides detailed statistics about mask creation and processing")

    print("\nKey differences from prepare_training_data():")
    print("• No image preprocessing - copies original images")
    print("• Converts bounding box labels to polygon segmentation masks")
    print("• Output labels are in YOLO segmentation format instead of bounding box format")
    print("• Uses prepare_yolo_masks() function for mask generation")
    print("• Suitable for training segmentation models instead of detection models")

    print("\nTo actually prepare segmentation data, set dry_run=False")

# Example usage for training data preparation with preprocessing
if __name__ == "__main__" and False:  # Set to True to run demo
    demo_training_data_preparation_with_preprocessing()

# Example usage for segmentation data preparation
if __name__ == "__main__" and False:  # Set to True to run segmentation demo
    demo_segmentation_data_preparation()




