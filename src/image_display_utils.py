"""
Image Display Utilities

This module contains functions for displaying and visualizing images from the 
sonic logging dataset, specifically for showing full and initial images side by side.
"""

import random
import matplotlib.pyplot as plt
import cv2
import os
import numpy as np

# You'll need to import or define these functions from your existing code
# Assuming these are available in your environment
try:
    from concave_hull import concave_hull
except ImportError:
    print("Warning: concave_hull not available")

# Constants (adjust as needed)
DISTANCE_THRESHOLD = 300


def remove_gridlines(rgb_image: np.ndarray, width_to_adjust=200, height_to_adjust=5):
    gray = cv2.cvtColor(rgb_image, cv2.COLOR_RGB2GRAY)
    gray = cv2.bitwise_not(gray)
    bw = cv2.adaptiveThreshold(gray, 255, cv2.ADAPTIVE_THRESH_MEAN_C, \
                                cv2.THRESH_BINARY, 15, -2)

    horizontal = np.copy(bw)
    vertical = np.copy(bw)
    morph_kernel = np.ones((3, 3),np.uint8)

    # Specify size on horizontal axis
    cols = horizontal.shape[1]
    horizontal_size = cols // width_to_adjust
    horizontalStructure = cv2.getStructuringElement(cv2.MORPH_RECT, (horizontal_size, 1))
    horizontal = cv2.erode(horizontal, horizontalStructure)
    horizontal = cv2.dilate(horizontal, horizontalStructure)

    # Specify size on vertical axis
    rows = vertical.shape[0]
    verticalsize = rows // height_to_adjust
    verticalStructure = cv2.getStructuringElement(cv2.MORPH_RECT, (1, verticalsize))
    vertical = cv2.erode(vertical, verticalStructure)
    vertical = cv2.dilate(vertical, verticalStructure)

    filter_mask = cv2.bitwise_or(horizontal, vertical)
    inverse_mask = cv2.bitwise_not(filter_mask)
    process_image = cv2.bitwise_and(bw, bw, mask=inverse_mask)
    process_opening = cv2.morphologyEx(process_image, cv2.MORPH_OPEN, morph_kernel)

    connectivity_process_filter_image = cv2.medianBlur(process_opening, 3)
    process_close = cv2.morphologyEx(connectivity_process_filter_image, cv2.MORPH_CLOSE, morph_kernel)

    return np.array(process_close).astype(np.uint8)


def threshold_image(process_image):
    """Threshold the image to highlight peaks and return the thresholded image."""
    three_channel_process_image = np.stack([process_image] * 3, axis=-1)
    gray_img = cv2.cvtColor(three_channel_process_image, cv2.COLOR_RGB2GRAY)
    _, thresholded_img = cv2.threshold(gray_img, 200, 255, cv2.THRESH_BINARY)
    return thresholded_img, three_channel_process_image


def get_all_peaks(thresholded_img, is_spatial=False):
    """
    Get all peak points from the thresholded image.

    Args:
        thresholded_img: Binary image where peaks are white pixels (value = 255)
        is_spatial (bool): If True, return peaks as spatial 2D data preserving image structure.
                          If False, return as flattened array of coordinates.

    Returns:
        If is_spatial=False: numpy array of shape (N, 2) with coordinates [[y1, x1], [y2, x2], ...]
        If is_spatial=True: 2D numpy array same shape as input image with 1s at peak locations, 0s elsewhere
    """
    if is_spatial:
        # Return spatial 2D data - preserve the image structure
        spatial_peaks = (thresholded_img == 255).astype(np.uint8)
        return spatial_peaks
    else:
        # Return flattened array of coordinates (original behavior)
        peaks = np.column_stack(np.where(thresholded_img == 255))  # Get all peaks (non-zero pixels)
        return peaks




def calculate_middle_samples(thresholded_img, peaks, offset_x=50):
    """Calculate middle samples from peaks."""
    # This is a simplified version - you may need to adjust based on your specific logic
    if len(peaks) == 0:
        return peaks

    # Group peaks by approximate y-coordinate and find middle points
    # This is a basic implementation - adjust as needed
    middle_samples = []
    for peak in peaks[::10]:  # Sample every 10th peak to reduce density
        y, x = peak
        if x > offset_x:
            middle_samples.append([y, x])

    return np.array(middle_samples) if middle_samples else peaks


def process_image_with_hulls(
        process_image, is_middle_point=False,
        distance_threshold=50, offset_x=25):
    """Process image and return detailed analysis data."""
    thresholded_img, three_channel_process_image = threshold_image(process_image)

    peaks = get_all_peaks(thresholded_img)
    # y, x coordinates

    # Filter out peaks with x < 5% of the width
    width = process_image.shape[1]
    filtered_peaks = filter_peaks_by_x_coordinate(peaks, width, threshold_ratio=0.10)

    middle_samples = None
    # If middle point calculation is enabled, calculate the middle samples
    if is_middle_point:
        middle_samples = calculate_middle_samples(thresholded_img, filtered_peaks, offset_x=offset_x)
        filtered_peaks = middle_samples

    filtered_peaks = filter_peaks_by_distance(filtered_peaks, distance_threshold)
    highlighted_img_concave = calculate_concave_hull(filtered_peaks, three_channel_process_image)

    return {
        'peaks': peaks,
        'filtered_peaks': filtered_peaks,
        'middle_samples': middle_samples,
        'concave_vertices': filtered_peaks,  # The final filtered peaks are the concave vertices
        'highlighted_image': highlighted_img_concave,
        'original_processed': three_channel_process_image
    }


def process_image_with_convex_hulls(
        remove_gridline_image, is_middle_point=False,
        distance_threshold=50, offset_x=25):
    """
    Process image using convex hull instead of concave hull and return detailed analysis data.

    This function follows the same processing pipeline as process_image_with_hulls() but uses
    convex hull calculation to enclose the vertices.

    Args:
        process_image: Input image to process
        is_middle_point (bool): Whether to calculate middle samples
        distance_threshold (int): Distance threshold for filtering peaks
        offset_x (int): Offset for middle sample calculation

    Returns:
        dict: Dictionary containing the same structure as process_image_with_hulls():
            - 'peaks': All detected peaks
            - 'filtered_peaks': Peaks after filtering
            - 'middle_samples': Middle samples if calculated
            - 'convex_vertices': Final filtered peaks (convex vertices)
            - 'highlighted_image': Image with convex hull overlay
            - 'original_processed': Original processed image
    """
    thresholded_img, three_channel_process_image = threshold_image(remove_gridline_image)

    spatial_peaks = get_all_peaks(thresholded_img, is_spatial=True)
    peaks = get_all_peaks(thresholded_img, is_spatial=False)

    # Filter out peaks with x < 5% of the width
    width = remove_gridline_image.shape[1]
    filtered_peaks = filter_peaks_by_neighborhood(spatial_peaks, x_span=100, y_span=100, density_threshold=0.01)
    filtered_peaks = filter_peaks_by_x_coordinate(filtered_peaks, width, threshold_ratio=0.01)

    middle_samples = None
    # If middle point calculation is enabled, calculate the middle samples
    if is_middle_point:
        middle_samples = calculate_middle_samples(thresholded_img, filtered_peaks, offset_x=offset_x)
        filtered_peaks = middle_samples

    # filtered_peaks = filter_peaks_by_distance(filtered_peaks, distance_threshold)
    highlighted_img_convex = calculate_convex_hull(filtered_peaks, three_channel_process_image)
    # highlighted_img_convex=plot_peaks_on_image(highlighted_img_convex, filtered_peaks)

    return {
        'peaks': peaks,
        'filtered_peaks': filtered_peaks,
        'middle_samples': middle_samples,
        'convex_vertices': filtered_peaks,  # The final filtered peaks are the convex vertices
        'highlighted_image': highlighted_img_convex,
        'original_processed': three_channel_process_image
    }


def parse_polylines_string(polylines_str):
    """Parse polylines string from dataframe into list of coordinate arrays."""
    try:
        import ast
        # Safely evaluate the string representation of the list
        polylines_list = ast.literal_eval(polylines_str)
        return polylines_list
    except:
        # If parsing fails, return empty list
        return []


def draw_polylines_on_image(image, polylines_list):
    """Draw polylines from label data on the image."""
    if polylines_list is None or len(polylines_list) == 0:
        return image

    highlighted_image = image.copy()

    # Convert to 3-channel if needed
    if len(highlighted_image.shape) == 2:
        highlighted_image = np.stack([highlighted_image] * 3, axis=-1)

    # Parse polylines if it's a string
    if isinstance(polylines_list, str):
        polylines_list = parse_polylines_string(polylines_list)

    # Draw each polyline
    for polyline in polylines_list:
        if len(polyline) >= 2:
            # Convert polyline points to integer coordinates
            points = np.array(polyline, dtype=np.int32)

            # Draw polyline in blue color
            cv2.polylines(highlighted_image, [points], isClosed=False, color=(255 , 0, 0), thickness=3)

            # Optionally draw points
            for point in points:
                cv2.circle(highlighted_image, tuple(point), 2, (0, 255, 255), -1)  # Yellow dots for polyline points

    return highlighted_image


def draw_highlights_on_image(target_image, analysis_data, polylines_list=None):
    """Draw highlights (peaks, middle samples, concave hull, and polylines) on target image."""
    highlighted_image = target_image.copy()

    # Convert to 3-channel if needed
    if len(highlighted_image.shape) == 2:
        highlighted_image = np.stack([highlighted_image] * 3, axis=-1)

    # Draw concave hull first
    if analysis_data['concave_vertices'] is not None and len(analysis_data['concave_vertices']) > 0:
        # Draw concave hull
        concave_vertices = analysis_data['concave_vertices'].astype(np.int32)
        highlighted_image = calculate_concave_hull(concave_vertices, highlighted_image)

    # Draw polylines on top - DISABLED
    if polylines_list is not None:
        highlighted_image = draw_polylines_on_image(highlighted_image, polylines_list)

    return highlighted_image


def get_init_peak_from_initial_to_full(full_image, initial_image, polylines_list=None):
    """
    Calculate peaks, middle_samples, concave vertices from initial_image
    and draw highlights on full_image, including polylines from label data.
    """
    # Remove gridlines from both images
    remove_grid_full_image = remove_gridlines(full_image)
    remove_grid_init_image = remove_gridlines(initial_image)

    # Analyze the initial image to get peaks, middle samples, and concave vertices
    initial_analysis = process_image_with_hulls(
        remove_grid_init_image,
        is_middle_point=True,
        distance_threshold=DISTANCE_THRESHOLD
    )

    full_analysis = process_image_with_convex_hulls(
        remove_grid_full_image,
        distance_threshold=DISTANCE_THRESHOLD
    )

    # Draw the highlights from initial image analysis onto the full image, including polylines
    # highlighted_full_image = draw_highlights_on_image(remove_grid_full_image, initial_analysis, polylines_list)
    highlighted_full_image = draw_highlights_on_image(remove_grid_full_image, full_analysis, polylines_list)
    highlighted_full_image = plot_peaks_on_image(highlighted_full_image, full_analysis['filtered_peaks'])

    # Return the highlighted full image and the processed initial image
    return highlighted_full_image, full_analysis['highlighted_image']


def show_full_and_initial_images(jpg_files, init_files, fold_name='fold_1_03052025', num_pairs=1, label_df=None):
    """
    Display full and initial images side by side with polylines from label data.

    This function randomly selects full images from the specified fold and finds
    their corresponding initial images (which have " (Initial)" in the filename).
    It displays them side by side in a 2-column layout with polylines overlaid.

    Args:
        jpg_files (dict): Dictionary containing full image paths organized by fold.
                         Expected structure: jpg_files[fold_name]['full']
        init_files (list): List of initial image file paths
        fold_name (str): Name of the fold to select from (default: 'fold_1_03052025')
        num_pairs (int): Number of image pairs to display (default: 1)
        label_df (DataFrame): DataFrame with columns 'image_name' and 'all_polylines'

    Returns:
        None: Displays the images using matplotlib

    Example:
        >>> # Display 1 pair of images
        >>> show_full_and_initial_images(jpg_files, init_files)

        >>> # Display 2 pairs with polylines
        >>> show_full_and_initial_images(jpg_files, init_files, fold_name='fold_1_03052025', num_pairs=2, label_df=df)

        >>> # Display 3 pairs from fold 2
        >>> show_full_and_initial_images(jpg_files, init_files, fold_name='fold_2_03052025', num_pairs=3)
    """
    
    # Validate input parameters
    if not isinstance(jpg_files, dict):
        print("Error: jpg_files must be a dictionary")
        return

    if not isinstance(init_files, list):
        print("Error: init_files must be a list")
        return

    if not isinstance(num_pairs, int) or num_pairs < 1:
        print("Error: num_pairs must be a positive integer")
        return

    # Get full images from the specified fold
    if fold_name not in jpg_files:
        print(f"Fold '{fold_name}' not found in jpg_files")
        print(f"Available folds: {list(jpg_files.keys())}")
        return

    if 'full' not in jpg_files[fold_name]:
        print(f"'full' not found in {fold_name}")
        print(f"Available keys in {fold_name}: {list(jpg_files[fold_name].keys())}")
        return

    full_images = jpg_files[fold_name]['full']
    initial_images = init_files  # Use the separate init_files list
    
    if len(full_images) == 0:
        print(f"No full images available in {fold_name}")
        return
    
    if len(initial_images) == 0:
        print(f"No initial images available in {fold_name}")
        return
    
    # Adjust num_pairs if there aren't enough full images
    if num_pairs > len(full_images):
        print(f"Warning: Only {len(full_images)} full images available, reducing num_pairs to {len(full_images)}")
        num_pairs = len(full_images)
    
    # Create figure with subplots (2 columns for each pair)
    fig, axes = plt.subplots(num_pairs, 2, figsize=(12, 6 * num_pairs))
    
    # Handle case when num_pairs = 1 (axes won't be 2D)
    if num_pairs == 1:
        axes = axes.reshape(1, -1)
    
    for i in range(num_pairs):
        # Randomly select a full image
        full_image_path = random.choice(full_images)
        full_filename = os.path.basename(full_image_path)

        # Generate corresponding initial filename
        # Convert from "2STOREY.1_page_1.jpg" to "2STOREY.1 (Initial)_page_1.jpg"
        initial_filename = full_filename.replace("_page_", " (Initial)_page_")

        # Find the corresponding initial image
        initial_image_path = None
        for init_path in initial_images:
            if os.path.basename(init_path) == initial_filename:
                initial_image_path = init_path
                break

        # Get polylines for this image from label dataframe
        polylines_list = None
        if label_df is not None:
            # Look for the image in the dataframe
            matching_rows = label_df[label_df['image/name'] == full_filename]
            if not matching_rows.empty:
                polylines_list = matching_rows.iloc[0]['all_polylines']
                print(f"Found {len(polylines_list) if polylines_list else 0} polylines for {full_filename}")

        # Load images and apply processing
        full_img = cv2.imread(full_image_path)
        initial_img = cv2.imread(initial_image_path) if initial_image_path else None

        if full_img is not None and initial_img is not None:
            # Convert to RGB
            full_img_rgb = cv2.cvtColor(full_img, cv2.COLOR_BGR2RGB)
            initial_img_rgb = cv2.cvtColor(initial_img, cv2.COLOR_BGR2RGB)

            # Apply processing with polylines - returns ready-to-display data
            processed_full, processed_initial = get_init_peak_from_initial_to_full(
                full_img_rgb, initial_img_rgb, polylines_list
            )

            # Display processed images
            axes[i, 0].imshow(processed_full)
            title_suffix = f" + {len(polylines_list)} polylines" if polylines_list else ""
            axes[i, 0].set_title(f'Full: {full_filename}{title_suffix}')
            axes[i, 0].axis('off')

            axes[i, 1].imshow(processed_initial)
            axes[i, 1].set_title(f'Initial: {initial_filename}')
            axes[i, 1].axis('off')
        else:
            # Handle missing images
            axes[i, 0].text(0.5, 0.5, 'Image not found', ha='center', va='center')
            axes[i, 0].set_title(f'Full: {full_filename}')
            axes[i, 0].axis('off')

            axes[i, 1].text(0.5, 0.5, 'Image not found', ha='center', va='center')
            axes[i, 1].set_title(f'Initial: {initial_filename}')
            axes[i, 1].axis('off')

    
    plt.tight_layout()
    plt.show()


def get_available_folds(jpg_files):
    """
    Get a list of available folds in the jpg_files dictionary.
    
    Args:
        jpg_files (dict): Dictionary containing image paths organized by fold
        
    Returns:
        list: List of available fold names
    """
    if not isinstance(jpg_files, dict):
        return []
    return list(jpg_files.keys())


def get_fold_statistics(jpg_files, fold_name=None):
    """
    Get statistics about images in folds.
    
    Args:
        jpg_files (dict): Dictionary containing image paths organized by fold
        fold_name (str, optional): Specific fold to get stats for. If None, gets stats for all folds.
        
    Returns:
        dict: Statistics about the images
    """
    if not isinstance(jpg_files, dict):
        return {}
    
    if fold_name:
        if fold_name not in jpg_files:
            return {}
        
        fold_data = jpg_files[fold_name]
        return {
            fold_name: {
                'full_count': len(fold_data.get('full', [])),
                'initial_count': len(fold_data.get('initial', []))
            }
        }
    else:
        stats = {}
        for fold in jpg_files:
            fold_data = jpg_files[fold]
            stats[fold] = {
                'full_count': len(fold_data.get('full', [])),
                'initial_count': len(fold_data.get('initial', []))
            }
        return stats



def threshold_image(process_image):
    """Threshold the image to highlight peaks and return the thresholded image."""
    three_channel_process_image = np.stack([process_image] * 3, axis=-1)
    gray_img = cv2.cvtColor(three_channel_process_image, cv2.COLOR_RGB2GRAY)
    _, thresholded_img = cv2.threshold(gray_img, 200, 255, cv2.THRESH_BINARY)
    return thresholded_img, three_channel_process_image


def calculate_middle_samples(thresholded_img, peaks, offset_x=10):
    """Calculate the middle sample (peak) for each row (trace) and generate new peaks."""
    middle_samples = []
    for row in range(thresholded_img.shape[0]):  # For each row (trace)
        row_peaks = peaks[peaks[:, 1] == row]  # Get all peaks for the current row (filter by y-value)
        if row_peaks.shape[0] > 0:
            peak_x = row_peaks[:, 0].mean()  # Calculate the middle sample (mean of x-coordinates)
            
            # Generate two new peaks based on the middle point
            new_peaks = [
                (peak_x - offset_x, row),  # Peak at x - offset_x
                (peak_x + offset_x, row)   # Peak at x + offset_x
            ]
            middle_samples.extend(new_peaks)  # Add the new peaks to the list

    return np.array(middle_samples)



def get_all_peaks(thresholded_img, is_spatial=False):
    """
    Get all peak points from the thresholded image.

    Args:
        thresholded_img: Binary image where peaks are white pixels (value = 255)
        is_spatial (bool): If True, return peaks as spatial 2D data preserving image structure.
                           If False, return as flattened array of coordinates.

    Returns:
        If is_spatial=False: numpy array of shape (N, 2) with coordinates [[x1, y1], [x2, y2], ...]
        If is_spatial=True: 2D numpy array same shape as input image with 1s at peak locations, 0s elsewhere
    """
    if is_spatial:
        # Return spatial 2D data - preserve the image structure
        spatial_peaks = (thresholded_img == 255).astype(np.uint8)
        return spatial_peaks
    else:
        # Return flattened array of coordinates (original behavior)
        peaks = np.column_stack(np.where(thresholded_img == 255))  # Get all peaks (non-zero pixels)
        peaks = peaks[:, [1, 0]]  # Convert from (y, x) to (x, y) format
        return peaks

def filter_peaks_by_x_coordinate(peaks, width, threshold_ratio=0.05):
    """Filter out peaks with x < threshold_ratio * width."""
    threshold_x = width * threshold_ratio
    filtered_peaks = peaks[peaks[:, 0] >= threshold_x]
    return filtered_peaks

def filter_peaks_by_distance(peaks, distance_threshold=50):
    """Filter peaks based on a distance threshold to exclude outliers or points too far apart."""
    filtered_peaks = [peaks[0]]  # Always keep the first peak
    for i in range(1, len(peaks)):
        prev_peak = filtered_peaks[-1]
        curr_peak = peaks[i]
        # Calculate the Euclidean distance between the current peak and the previous one
        distance = np.linalg.norm(curr_peak - prev_peak)
        
        if distance < distance_threshold:  # Keep the sample if it's within the threshold distance
            filtered_peaks.append(curr_peak)

    return np.array(filtered_peaks)
def filter_peaks_by_neighborhood(spatial_peaks, x_span, y_span, density_threshold=0.3):
    """ 
    spatial_peaks: 2D as y, x
    """
    H, W = spatial_peaks.shape
    keep_coords = []

    peak_coords = np.argwhere(spatial_peaks == 1) # (y, x) format

    for y, x in peak_coords:
        x_min = max(0, x - x_span // 2)
        x_max = min(W, x + x_span // 2)
        y_min = max(0, y - y_span // 2)
        y_max = min(H, y + y_span // 2)

        window = spatial_peaks[y_min : y_max, x_min : x_max]
        total_pixels = window.size
        count = np.sum(window)

        if count / total_pixels > density_threshold:
            keep_coords.append((x, y))

    # Return flatten array as x, y
    return np.array(keep_coords) 

def calculate_convex_hull(peaks, three_channel_process_image):
    """Calculate the convex hull for the filtered points and return the highlighted image."""
    # peaks is stored as (y, x) format, but cv2.convexHull expects (x, y) format
    # Convert from (y, x) to (x, y) by swapping columns
    if len(peaks) > 0:
        peaks_xy = peaks[:, [1, 0]]  # Swap columns: (y, x) -> (x, y)
    else:
        peaks_xy = peaks

    convex_hull = cv2.convexHull(peaks_xy.astype(np.int32))
    highlighted_img_convex = three_channel_process_image.copy()
    cv2.fillPoly(highlighted_img_convex, [convex_hull], (255, 0, 0))

    # Apply transparency to the red overlay
    alpha = 0.1  # Lower value for more transparency
    overlay_convex = highlighted_img_convex.copy()
    cv2.addWeighted(overlay_convex, alpha, three_channel_process_image, 1 - alpha, 0, highlighted_img_convex)

    return highlighted_img_convex

def calculate_concave_hull(peaks, three_channel_process_image, concavity_alpha=4.0):
    """Calculate the concave hull using the ConcaveHull library and return the highlighted image."""
    # Apply ConcaveHull to the full set of points (peaks)
    concave_hull_points = concave_hull(peaks, concavity=concavity_alpha)
    
    # Create a copy of the image to apply concave hull
    highlighted_img_concave = three_channel_process_image.copy()
    
    # Draw the concave hull on the image (convert to integer coordinates)
    if len(concave_hull_points) > 0:
        concave_hull_coords = np.array(concave_hull_points, dtype=np.int32)
        cv2.fillPoly(highlighted_img_concave, [concave_hull_coords], (0, 255, 0))  # Green concave hull
    
    # Apply transparency to the concave hull overlay
    alpha = 0.3  # Lower value for more transparency
    overlay_concave = highlighted_img_concave.copy()
    cv2.addWeighted(overlay_concave, alpha, three_channel_process_image, 1 - alpha, 0, highlighted_img_concave)
    
    return highlighted_img_concave


def plot_peaks_on_image(original_img, peaks):
    """Plot the detected peaks (samples) as red points on the original image."""
    img_with_peaks = original_img.copy()
    for peak in peaks:
        x, y = peak
        # Ensure that x and y are integers before passing them to cv2.circle
        x, y = int(x), int(y)
        # Draw a red circle at each peak location
        cv2.circle(img_with_peaks, (x, y), 5, (255, 0, 0), -1)
    return img_with_peaks

def display_images(original_img, highlighted_img_convex, highlighted_img_concave, peaks):
    """Display the original image with peaks, convex hull overlay, and concave hull overlay."""
    # Plot the original image with the detected peaks
    img_with_peaks = plot_peaks_on_image(original_img, peaks)
    
    # Display original image with peaks and convex hull
    fig, axes = plt.subplots(1, 2, figsize=(12, 6))

    axes[0].imshow(img_with_peaks)
    axes[0].set_title('Original Image with Peaks')
    axes[0].axis('off')

    axes[1].imshow(highlighted_img_concave)
    axes[1].set_title('Enhanced Image with Concave Hull Overlay')
    axes[1].axis('off')

    plt.show()

    # Display the image with convex hull
    fig, axes = plt.subplots(1, 2, figsize=(12, 6))

    axes[0].imshow(img_with_peaks)
    axes[0].set_title('Original Image with Peaks')
    axes[0].axis('off')

    axes[1].imshow(highlighted_img_convex)
    axes[1].set_title('Enhanced Image with Convex Hull Overlay')
    axes[1].axis('off')

    plt.show()

def check_peaks_distance(peaks, distance_threshold=50):
    """Check if any peaks are too far apart and print the results."""
    far_peaks = []
    for i in range(1, len(peaks)):
        prev_peak = peaks[i-1]
        curr_peak = peaks[i]
        # Calculate the Euclidean distance between the current peak and the previous one
        distance = np.linalg.norm(curr_peak - prev_peak)
        
        if distance > distance_threshold:  # If distance exceeds threshold, record the peaks
            far_peaks.append((prev_peak, curr_peak))
    
    # Print the far-apart peaks
    if far_peaks:
        print(f"Found {len(far_peaks)} peaks that are too far apart (distance > {distance_threshold}):")
        for pair in far_peaks:
            print(f"Peak 1: {pair[0]}, Peak 2: {pair[1]}")
    else:
        print(f"No peaks found that are far apart (distance > {distance_threshold}).")

# PREPARE DATA FOR YOLO
def find_polylines_intersections(polylines_list, image_width=None, image_height=None, offset_x=30):
    """
    Find intersection points between polylines with preprocessing to ensure reliability.

    Args:
        polylines_list: List of polylines, each polyline is a list of (x, y) coordinates
        image_width: Width of the image for boundary checking
        image_height: Height of the image for boundary checking
        offset_x: Minimum distance from image edges (default: 30 pixels)

    Returns:
        list: List of reliable intersection points as (x, y) tuples
    """
    intersections = []

    if len(polylines_list) < 2:
        return intersections

    # Check intersections between all pairs of polylines
    for i in range(len(polylines_list)):
        for j in range(i + 1, len(polylines_list)):
            polyline1 = polylines_list[i]
            polyline2 = polylines_list[j]

            # Check intersection between each segment of polyline1 and polyline2
            for k in range(len(polyline1) - 1):
                for l in range(len(polyline2) - 1):
                    # Get line segments
                    p1, p2 = polyline1[k], polyline1[k + 1]
                    p3, p4 = polyline2[l], polyline2[l + 1]

                    # Find intersection point
                    intersection = line_intersection(p1, p2, p3, p4)
                    if intersection:
                        # Apply preprocessing to ensure intersection point reliability
                        if is_reliable_intersection(intersection, image_width, image_height, offset_x):
                            intersections.append(intersection)

    return intersections


def is_reliable_intersection(intersection, image_width, image_height, offset_x=30):
    """
    Check if an intersection point is reliable based on image boundaries and features.

    Args:
        intersection: (x, y) coordinates of intersection point
        image_width: Width of the image
        image_height: Height of the image
        offset_x: Minimum distance from image edges

    Returns:
        bool: True if intersection is reliable, False otherwise
    """
    if image_width is None or image_height is None:
        return True  # Skip validation if image dimensions not provided

    x, y = intersection

    # Check if intersection is within reliable bounds (offset_x pixels from edges)
    if (x < offset_x or
        x > image_width - offset_x or
        y < offset_x or
        y > image_height - offset_x):
        return False

    # Additional reliability checks can be added here:
    # - Check if intersection is on actual image features
    # - Validate intersection angle
    # - Check local image contrast

    return True


def line_intersection(p1, p2, p3, p4):
    """
    Find intersection point between two line segments.

    Args:
        p1, p2: Points defining first line segment
        p3, p4: Points defining second line segment

    Returns:
        tuple or None: Intersection point (x, y) or None if no intersection
    """
    x1, y1 = p1
    x2, y2 = p2
    x3, y3 = p3
    x4, y4 = p4

    # Calculate the denominator
    denom = (x1 - x2) * (y3 - y4) - (y1 - y2) * (x3 - x4)

    if abs(denom) < 1e-10:  # Lines are parallel
        return None

    # Calculate intersection point
    t = ((x1 - x3) * (y3 - y4) - (y1 - y3) * (x3 - x4)) / denom
    u = -((x1 - x2) * (y1 - y3) - (y1 - y2) * (x1 - x3)) / denom

    # Check if intersection is within both line segments
    if 0 <= t <= 1 and 0 <= u <= 1:
        x = x1 + t * (x2 - x1)
        y = y1 + t * (y2 - y1)
        return (x, y)

    return None


def convert_polylines_to_yolo_keypoints(polylines_list, image_width, image_height, offset_x=30):
    """
    Convert polylines to YOLO keypoint format based on reliable intersection points.

    Args:
        polylines_list: List of polylines, each polyline is a list of (x, y) coordinates
        image_width: Width of the image
        image_height: Height of the image
        offset_x: Minimum distance from image edges for reliable intersections (default: 30 pixels)

    Returns:
        list: List of YOLO format strings for each reliable intersection point
    """
    if not polylines_list or len(polylines_list) == 0:
        return []

    # Find reliable intersection points with preprocessing
    intersections = find_polylines_intersections(
        polylines_list,
        image_width=image_width,
        image_height=image_height,
        offset_x=offset_x
    )

    if not intersections:
        return []

    yolo_lines = []
    bbox_size = 20  # 20 pixels for both width and height

    print(f"    Found {len(intersections)} reliable intersection points (offset_x={offset_x}px)")

    for intersection in intersections:
        x, y = intersection

        # Calculate bounding box centered on intersection point
        # Normalize coordinates to [0, 1]
        x_center = x / image_width
        y_center = y / image_height
        width = bbox_size / image_width
        height = bbox_size / image_height

        # Keypoint coordinates (same as center since we only have 1 keypoint)
        keypoint_x = x_center
        keypoint_y = y_center
        keypoint_v = 2  # visible

        # YOLO format: class_id x_center y_center width height keypoint1_x keypoint1_y keypoint1_v
        yolo_line = f"0 {x_center:.6f} {y_center:.6f} {width:.6f} {height:.6f} {keypoint_x:.6f} {keypoint_y:.6f} {keypoint_v}"
        yolo_lines.append(yolo_line)

    return yolo_lines


def prepare_yolo_keypoint_training_data(
        jpg_files, init_files, label_df,
        output_base_dir=".", negative_sample_prepare=False, number_of_negative_sampling=None, offset_x=30):
    """
    Prepare YOLO keypoint detection training data from the existing image processing pipeline.

    Args:
        jpg_files (dict): Dictionary containing image paths organized by fold
        init_files (list): List of initial image file paths
        label_df (DataFrame): DataFrame with 'image_name' and 'all_polylines' columns
        output_base_dir (str): Base directory for saving training data
        negative_sample_prepare (bool): Whether to process unlabeled images as negative samples
        number_of_negative_sampling (int): Maximum number of negative samples to prepare (None for unlimited)
        offset_x (int): Minimum distance from image edges for reliable intersections (default: 30 pixels)

    Returns:
        dict: Summary statistics of the processing
    """
    import pandas as pd
    from pathlib import Path
    import shutil

    # Create output directories
    images_dir = Path(output_base_dir) / "images" / "train"
    labels_dir = Path(output_base_dir) / "labels" / "train"
    images_dir.mkdir(parents=True, exist_ok=True)
    labels_dir.mkdir(parents=True, exist_ok=True)

    # Initialize statistics
    stats = {
        'total_folds': 0,
        'total_full_images': 0,
        'processed_with_labels': 0,
        'processed_negative_samples': 0,
        'negative_samples_limit_reached': 0,
        'total_intersection_points': 0,
        'skipped_missing_initial': 0,
        'skipped_missing_full': 0,
        'skipped_processing_errors': 0,
        'skipped_no_intersections': 0,
        'errors': []
    }

    # Initialize negative sampling counter
    negative_samples_count = 0

    print("Starting YOLO keypoint training data preparation...")
    print(f"Configuration:")
    print(f"  Output base directory: {output_base_dir}")
    print(f"  Negative sample preparation: {negative_sample_prepare}")
    print(f"  Number of negative samples limit: {number_of_negative_sampling if number_of_negative_sampling is not None else 'Unlimited'}")
    print(f"  Intersection reliability offset: {offset_x} pixels")
    print(f"Output directories:")
    print(f"  Images: {images_dir}")
    print(f"  Labels: {labels_dir}")
    print("=" * 60)

    # Create a lookup dictionary for initial images
    init_files_dict = {}
    for init_path in init_files:
        init_filename = os.path.basename(init_path)
        # Extract the base name without " (Initial)" part
        base_name = init_filename.replace(" (Initial)", "")
        init_files_dict[base_name] = init_path

    # Process each fold
    for fold_name, fold_data in jpg_files.items():
        if 'full' not in fold_data:
            continue

        stats['total_folds'] += 1
        full_images = fold_data['full']
        stats['total_full_images'] += len(full_images)

        print(f"\nProcessing fold: {fold_name}")
        print(f"  Full images: {len(full_images)}")

        # Process each full image
        for idx, full_image_path in enumerate(full_images):
            full_filename = os.path.basename(full_image_path)
            base_name = os.path.splitext(full_filename)[0]

            try:
                # Find corresponding initial image
                initial_image_path = init_files_dict.get(full_filename)
                if not initial_image_path or not os.path.exists(initial_image_path):
                    stats['skipped_missing_initial'] += 1
                    print(f"    Skipped {full_filename}: No initial image found")
                    continue

                # Check if full image exists
                if not os.path.exists(full_image_path):
                    stats['skipped_missing_full'] += 1
                    print(f"    Skipped {full_filename}: Full image not found")
                    continue

                # Load images
                full_img = cv2.imread(full_image_path)
                initial_img = cv2.imread(initial_image_path)

                if full_img is None or initial_img is None:
                    stats['skipped_processing_errors'] += 1
                    error_msg = f"Failed to load images: {full_filename}"
                    stats['errors'].append(error_msg)
                    print(f"    Error: {error_msg}")
                    continue

                # Convert to RGB
                full_img_rgb = cv2.cvtColor(full_img, cv2.COLOR_BGR2RGB)
                initial_img_rgb = cv2.cvtColor(initial_img, cv2.COLOR_BGR2RGB)

                # Get image dimensions
                image_height, image_width = full_img_rgb.shape[:2]

                # Check for label data
                polylines_list = None
                has_labels = False
                if label_df is not None:
                    matching_rows = label_df[label_df['image/name'] == full_filename]
                    if not matching_rows.empty:
                        polylines_data = matching_rows.iloc[0]['all_polylines']
                        if isinstance(polylines_data, str):
                            polylines_list = parse_polylines_string(polylines_data)
                        else:
                            polylines_list = polylines_data
                        has_labels = True

                # Process image through the pipeline
                processed_full_image, _ = get_init_peak_from_initial_to_full(
                    full_img_rgb, initial_img_rgb, polylines_list
                )

                # Save processed image
                output_image_path = images_dir / f"{base_name}.jpg"
                # Convert RGB back to BGR for saving
                processed_full_bgr = cv2.cvtColor(processed_full_image, cv2.COLOR_RGB2BGR)
                cv2.imwrite(str(output_image_path), processed_full_bgr)

                # Generate and save YOLO label
                output_label_path = labels_dir / f"{base_name}.txt"

                if has_labels and polylines_list:
                    # Convert polylines to YOLO keypoint format (returns list of lines)
                    yolo_lines = convert_polylines_to_yolo_keypoints(
                        polylines_list, image_width, image_height, offset_x
                    )

                    if yolo_lines:
                        with open(output_label_path, 'w') as f:
                            for line in yolo_lines:
                                f.write(line + '\n')
                        stats['processed_with_labels'] += 1
                        stats['total_intersection_points'] += len(yolo_lines)
                        print(f"    ✓ Processed with labels: {full_filename} ({len(yolo_lines)} intersection points)")
                    else:
                        # No intersections found, treat as negative sample if enabled
                        if negative_sample_prepare:
                            # Check if we've reached the negative sampling limit
                            if number_of_negative_sampling is None or negative_samples_count < number_of_negative_sampling:
                                with open(output_label_path, 'w') as f:
                                    f.write("")  # Empty file for negative sample
                                stats['processed_negative_samples'] += 1
                                negative_samples_count += 1
                                print(f"    ✓ Processed as negative sample: {full_filename} (no intersections found)")
                            else:
                                # Skip if negative sampling limit reached
                                os.remove(output_image_path)  # Remove the saved image
                                stats['negative_samples_limit_reached'] += 1
                                print(f"    Skipped {full_filename}: Negative sampling limit reached ({number_of_negative_sampling})")
                                continue
                        else:
                            # Skip if no intersections and negative samples not requested
                            os.remove(output_image_path)  # Remove the saved image
                            stats['skipped_no_intersections'] += 1
                            print(f"    Skipped {full_filename}: No intersections found (negative_sample_prepare=False)")
                            continue
                else:
                    # Create empty label file for negative sample
                    if negative_sample_prepare:
                        # Check if we've reached the negative sampling limit
                        if number_of_negative_sampling is None or negative_samples_count < number_of_negative_sampling:
                            with open(output_label_path, 'w') as f:
                                f.write("")  # Empty file for negative sample
                            stats['processed_negative_samples'] += 1
                            negative_samples_count += 1
                            print(f"    ✓ Processed as negative sample: {full_filename}")
                        else:
                            # Skip if negative sampling limit reached
                            os.remove(output_image_path)  # Remove the saved image
                            stats['negative_samples_limit_reached'] += 1
                            print(f"    Skipped {full_filename}: Negative sampling limit reached ({number_of_negative_sampling})")
                            continue
                    else:
                        # Skip if no labels and negative samples not requested
                        os.remove(output_image_path)  # Remove the saved image
                        print(f"    Skipped {full_filename}: No labels (negative_sample_prepare=False)")
                        continue

            except Exception as e:
                stats['skipped_processing_errors'] += 1
                error_msg = f"Processing error for {full_filename}: {str(e)}"
                stats['errors'].append(error_msg)
                print(f"    Error: {error_msg}")
                continue

    # Print summary
    print("\n" + "=" * 60)
    print("YOLO KEYPOINT TRAINING DATA PREPARATION COMPLETE")
    print("=" * 60)
    print(f"Total folds processed: {stats['total_folds']}")
    print(f"Total full images found: {stats['total_full_images']}")
    print(f"Successfully processed with labels: {stats['processed_with_labels']}")
    print(f"Total intersection points detected: {stats['total_intersection_points']}")
    print(f"Successfully processed as negative samples: {stats['processed_negative_samples']}")
    print(f"Skipped (negative sampling limit reached): {stats['negative_samples_limit_reached']}")
    print(f"Skipped (missing initial image): {stats['skipped_missing_initial']}")
    print(f"Skipped (missing full image): {stats['skipped_missing_full']}")
    print(f"Skipped (no intersections found): {stats['skipped_no_intersections']}")
    print(f"Skipped (processing errors): {stats['skipped_processing_errors']}")

    if stats['errors']:
        print(f"\nErrors encountered ({len(stats['errors'])}):")
        for error in stats['errors'][:5]:  # Show first 5 errors
            print(f"  - {error}")
        if len(stats['errors']) > 5:
            print(f"  ... and {len(stats['errors']) - 5} more errors")

    print(f"\nOutput saved to:")
    print(f"  Images: {images_dir} ({len(list(images_dir.glob('*.jpg')))} files)")
    print(f"  Labels: {labels_dir} ({len(list(labels_dir.glob('*.txt')))} files)")

    print(f"\nYOLO Label Format:")
    print(f"  - Each reliable intersection point creates one detection")
    print(f"  - Intersection reliability: {offset_x}px minimum distance from edges")
    print(f"  - Bounding box: 20x20 pixels centered on intersection")
    print(f"  - Keypoint: Single point at intersection location")
    print(f"  - Format: class_id x_center y_center width height keypoint_x keypoint_y keypoint_v")

    return stats


def prepare_yolo_segmentation_training_data(
        jpg_files, init_files, label_df,
        output_base_dir=".", negative_sample_prepare=False, number_of_negative_sampling=None):
    """
    Prepare YOLO segmentation training data using convex hull vertices from the image processing pipeline.

    Args:
        jpg_files (dict): Dictionary containing image paths organized by fold
        init_files (list): List of initial image file paths
        label_df (DataFrame): DataFrame with 'image_name' and 'all_polylines' columns (optional for segmentation)
        output_base_dir (str): Base directory for saving training data
        negative_sample_prepare (bool): Whether to process unlabeled images as negative samples
        number_of_negative_sampling (int): Maximum number of negative samples to prepare (None for unlimited)

    Returns:
        dict: Summary statistics of the processing
    """
    import pandas as pd
    from pathlib import Path
    import shutil
    from tqdm import tqdm

    # Create output directories
    images_dir = Path(output_base_dir) / "images" / "train"
    labels_dir = Path(output_base_dir) / "labels" / "train"
    images_dir.mkdir(parents=True, exist_ok=True)
    labels_dir.mkdir(parents=True, exist_ok=True)

    # Initialize statistics
    stats = {
        'total_folds': 0,
        'total_full_images': 0,
        'processed_with_convex_hull': 0,
        'processed_negative_samples': 0,
        'negative_samples_limit_reached': 0,
        'total_convex_vertices': 0,
        'skipped_missing_initial': 0,
        'skipped_missing_full': 0,
        'skipped_processing_errors': 0,
        'skipped_no_vertices': 0,
        'errors': []
    }

    # Initialize negative sampling counter
    negative_samples_count = 0

    print("Starting YOLO segmentation training data preparation...")
    print(f"Configuration:")
    print(f"  Output base directory: {output_base_dir}")
    print(f"  Negative sample preparation: {negative_sample_prepare}")
    print(f"  Number of negative samples limit: {number_of_negative_sampling if number_of_negative_sampling is not None else 'Unlimited'}")
    print(f"Output directories:")
    print(f"  Images: {images_dir}")
    print(f"  Labels: {labels_dir}")
    print("=" * 60)

    # Create a lookup dictionary for initial images
    init_files_dict = {}
    for init_path in init_files:
        init_filename = os.path.basename(init_path)
        # Extract the base name without " (Initial)" part
        base_name = init_filename.replace(" (Initial)", "")
        init_files_dict[base_name] = init_path

    # Calculate total number of images for progress tracking
    total_images = 0
    for fold_name, fold_data in jpg_files.items():
        if 'full' in fold_data:
            total_images += len(fold_data['full'])

    print(f"Total images to process: {total_images}")

    # Create overall progress bar
    overall_pbar = tqdm(total=total_images, desc="Processing images", unit="img")

    # Process each fold
    for fold_name, fold_data in jpg_files.items():
        if 'full' not in fold_data:
            continue

        stats['total_folds'] += 1
        full_images = fold_data['full']
        stats['total_full_images'] += len(full_images)

        print(f"\nProcessing fold: {fold_name}")
        print(f"  Full images: {len(full_images)}")

        # Process each full image
        for idx, full_image_path in enumerate(full_images):
            full_filename = os.path.basename(full_image_path)
            base_name = os.path.splitext(full_filename)[0]

            # Update progress bar description
            overall_pbar.set_description(f"Processing {fold_name}: {full_filename}")

            try:
                # Find corresponding initial image
                initial_image_path = init_files_dict.get(full_filename)
                if not initial_image_path or not os.path.exists(initial_image_path):
                    stats['skipped_missing_initial'] += 1
                    overall_pbar.set_postfix(status="Missing initial", refresh=False)
                    overall_pbar.update(1)
                    print(f"    Skipped {full_filename}: No initial image found")
                    continue

                # Check if full image exists
                if not os.path.exists(full_image_path):
                    stats['skipped_missing_full'] += 1
                    overall_pbar.set_postfix(status="Missing full", refresh=False)
                    overall_pbar.update(1)
                    print(f"    Skipped {full_filename}: Full image not found")
                    continue

                # Load images
                overall_pbar.set_postfix(status="Loading images", refresh=False)
                full_img = cv2.imread(full_image_path)
                initial_img = cv2.imread(initial_image_path)

                if full_img is None or initial_img is None:
                    stats['skipped_processing_errors'] += 1
                    error_msg = f"Failed to load images: {full_filename}"
                    stats['errors'].append(error_msg)
                    overall_pbar.set_postfix(status="Load error", refresh=False)
                    overall_pbar.update(1)
                    print(f"    Error: {error_msg}")
                    continue

                # Convert to RGB
                full_img_rgb = cv2.cvtColor(full_img, cv2.COLOR_BGR2RGB)
                initial_img_rgb = cv2.cvtColor(initial_img, cv2.COLOR_BGR2RGB)

                # Get image dimensions
                image_height, image_width = full_img_rgb.shape[:2]

                # Process image through convex hull pipeline (no drawing, only gridline removal)
                overall_pbar.set_postfix(status="Removing gridlines", refresh=False)
                remove_grid_full_image = remove_gridlines(full_img_rgb)
                remove_grid_init_image = remove_gridlines(initial_img_rgb)

                # Analyze the initial image to get convex hull vertices
                overall_pbar.set_postfix(status="Convex hull analysis", refresh=False)
                # This should be calculate fro full not initial
                initial_analysis = process_image_with_convex_hulls(
                    remove_grid_init_image,
                    is_middle_point=True,
                    distance_threshold=DISTANCE_THRESHOLD
                )

                full_analysis = process_image_with_convex_hulls(
                    remove_grid_full_image,
                    distance_threshold=DISTANCE_THRESHOLD
                )

                # Save processed image (only gridlines removed, no overlays)
                overall_pbar.set_postfix(status="Saving image", refresh=False)
                output_image_path = images_dir / f"{base_name}.jpg"
                # Convert RGB back to BGR for saving
                processed_full_bgr = cv2.cvtColor(remove_grid_full_image, cv2.COLOR_RGB2BGR)
                cv2.imwrite(str(output_image_path), processed_full_bgr)

                # Generate and save YOLO segmentation label
                overall_pbar.set_postfix(status="Generating labels", refresh=False)
                output_label_path = labels_dir / f"{base_name}.txt"

                # Get convex vertices from the analysis
                convex_vertices = full_analysis.get('convex_vertices', [])

                if convex_vertices is not None and len(convex_vertices) > 2:
                    # Convert convex vertices to YOLO segmentation format
                    yolo_segmentation_line = convert_vertices_to_yolo_segmentation(
                        convex_vertices, image_width, image_height
                    )

                    if yolo_segmentation_line:
                        with open(output_label_path, 'w') as f:
                            f.write(yolo_segmentation_line + '\n')
                        stats['processed_with_convex_hull'] += 1
                        stats['total_convex_vertices'] += len(convex_vertices)
                        overall_pbar.set_postfix(status=f"✓ Convex hull ({len(convex_vertices)} vertices)", refresh=False)
                        overall_pbar.update(1)
                        print(f"    ✓ Processed with convex hull: {full_filename} ({len(convex_vertices)} vertices)")
                    else:
                        # No valid vertices found, treat as negative sample if enabled
                        if negative_sample_prepare:
                            # Check if we've reached the negative sampling limit
                            if number_of_negative_sampling is None or negative_samples_count < number_of_negative_sampling:
                                with open(output_label_path, 'w') as f:
                                    f.write("")  # Empty file for negative sample
                                stats['processed_negative_samples'] += 1
                                negative_samples_count += 1
                                overall_pbar.set_postfix(status="✓ Negative sample", refresh=False)
                                overall_pbar.update(1)
                                print(f"    ✓ Processed as negative sample: {full_filename} (no valid vertices)")
                            else:
                                # Skip if negative sampling limit reached
                                os.remove(output_image_path)  # Remove the saved image
                                stats['negative_samples_limit_reached'] += 1
                                overall_pbar.set_postfix(status="Limit reached", refresh=False)
                                overall_pbar.update(1)
                                print(f"    Skipped {full_filename}: Negative sampling limit reached ({number_of_negative_sampling})")
                                continue
                        else:
                            # Skip if no vertices and negative samples not requested
                            os.remove(output_image_path)  # Remove the saved image
                            stats['skipped_no_vertices'] += 1
                            overall_pbar.set_postfix(status="No vertices", refresh=False)
                            overall_pbar.update(1)
                            print(f"    Skipped {full_filename}: No valid vertices (negative_sample_prepare=False)")
                            continue
                else:
                    # Create empty label file for negative sample
                    if negative_sample_prepare:
                        # Check if we've reached the negative sampling limit
                        if number_of_negative_sampling is None or negative_samples_count < number_of_negative_sampling:
                            with open(output_label_path, 'w') as f:
                                f.write("")  # Empty file for negative sample
                            stats['processed_negative_samples'] += 1
                            negative_samples_count += 1
                            overall_pbar.set_postfix(status="✓ Negative sample", refresh=False)
                            overall_pbar.update(1)
                            print(f"    ✓ Processed as negative sample: {full_filename}")
                        else:
                            # Skip if negative sampling limit reached
                            os.remove(output_image_path)  # Remove the saved image
                            stats['negative_samples_limit_reached'] += 1
                            overall_pbar.set_postfix(status="Limit reached", refresh=False)
                            overall_pbar.update(1)
                            print(f"    Skipped {full_filename}: Negative sampling limit reached ({number_of_negative_sampling})")
                            continue
                    else:
                        # Skip if no vertices and negative samples not requested
                        os.remove(output_image_path)  # Remove the saved image
                        overall_pbar.set_postfix(status="No vertices", refresh=False)
                        overall_pbar.update(1)
                        print(f"    Skipped {full_filename}: No vertices (negative_sample_prepare=False)")
                        continue

            except Exception as e:
                stats['skipped_processing_errors'] += 1
                error_msg = f"Processing error for {full_filename}: {str(e)}"
                stats['errors'].append(error_msg)
                overall_pbar.set_postfix(status="Error", refresh=False)
                overall_pbar.update(1)
                print(f"    Error: {error_msg}")
                continue

    # Close progress bar
    overall_pbar.close()

    # Print summary
    print("\n" + "=" * 60)
    print("YOLO SEGMENTATION TRAINING DATA PREPARATION COMPLETE")
    print("=" * 60)
    print(f"Total folds processed: {stats['total_folds']}")
    print(f"Total full images found: {stats['total_full_images']}")
    print(f"Successfully processed with convex hull: {stats['processed_with_convex_hull']}")
    print(f"Total convex vertices detected: {stats['total_convex_vertices']}")
    print(f"Successfully processed as negative samples: {stats['processed_negative_samples']}")
    print(f"Skipped (negative sampling limit reached): {stats['negative_samples_limit_reached']}")
    print(f"Skipped (missing initial image): {stats['skipped_missing_initial']}")
    print(f"Skipped (missing full image): {stats['skipped_missing_full']}")
    print(f"Skipped (no vertices found): {stats['skipped_no_vertices']}")
    print(f"Skipped (processing errors): {stats['skipped_processing_errors']}")

    if stats['errors']:
        print(f"\nErrors encountered ({len(stats['errors'])}):")
        for error in stats['errors'][:5]:  # Show first 5 errors
            print(f"  - {error}")
        if len(stats['errors']) > 5:
            print(f"  ... and {len(stats['errors']) - 5} more errors")

    print(f"\nOutput saved to:")
    print(f"  Images: {images_dir} ({len(list(images_dir.glob('*.jpg')))} files)")
    print(f"  Labels: {labels_dir} ({len(list(labels_dir.glob('*.txt')))} files)")

    print(f"\nYOLO Segmentation Label Format:")
    print(f"  - Each convex hull creates one segmentation mask")
    print(f"  - Vertices from convex hull analysis of initial image")
    print(f"  - Format: class_id x1 y1 x2 y2 ... xn yn (normalized coordinates)")
    print(f"  - Class ID: 0 (single class for sonic logging traces)")

    return stats


def convert_vertices_to_yolo_segmentation(vertices, image_width, image_height, class_id=0):
    """
    Convert convex hull vertices to YOLO segmentation format.

    Args:
        vertices: Array of vertices in (y, x) format from convex hull
        image_width: Width of the image
        image_height: Height of the image
        class_id: Class ID for the segmentation (default: 0)

    Returns:
        str: YOLO segmentation format string "class_id x1 y1 x2 y2 ... xn yn"
    """
    if vertices is None or len(vertices) < 3:
        return ""

    # Convert vertices to normalized coordinates
    normalized_coords = []

    for vertex in vertices:
        if len(vertex) >= 2:
            # vertices are in (y, x) format, convert to (x, y) and normalize
            y, x = vertex[0], vertex[1]

            # Normalize coordinates to [0, 1]
            norm_x = float(x) / image_width
            norm_y = float(y) / image_height

            # Clamp to [0, 1] range
            norm_x = max(0.0, min(1.0, norm_x))
            norm_y = max(0.0, min(1.0, norm_y))

            normalized_coords.extend([norm_x, norm_y])



    if len(normalized_coords) < 6:  # Need at least 3 points (6 coordinates)
        return ""

    # Format as YOLO segmentation: class_id x1 y1 x2 y2 ... xn yn
    coords_str = " ".join([f"{coord:.6f}" for coord in normalized_coords])
    return f"{class_id} {coords_str}"


def visualize_yolo_segmentation_data(images_dir, labels_dir, n_rows=2, n_cols=3, figsize=(15, 10)):
    """
    Randomly select and visualize YOLO segmentation training data with masks drawn on images.

    Args:
        images_dir (str): Path to directory containing training images
        labels_dir (str): Path to directory containing YOLO segmentation labels
        n_rows (int): Number of rows in the visualization grid (default: 2)
        n_cols (int): Number of columns in the visualization grid (default: 3)
        figsize (tuple): Figure size for matplotlib (default: (15, 10))

    Returns:
        None: Displays the visualization
    """
    import matplotlib.pyplot as plt
    import matplotlib.patches as patches
    from pathlib import Path
    import random
    import numpy as np

    # Convert to Path objects
    images_path = Path(images_dir)
    labels_path = Path(labels_dir)

    # Get all image files
    image_files = list(images_path.glob("*.jpg")) + list(images_path.glob("*.png"))

    if len(image_files) == 0:
        print(f"No image files found in {images_dir}")
        return

    # Calculate total number of subplots
    total_plots = n_rows * n_cols

    # Randomly select images
    selected_images = random.sample(image_files, min(total_plots, len(image_files)))

    # Create figure and subplots
    fig, axes = plt.subplots(n_rows, n_cols, figsize=figsize)

    # Handle case when n_rows or n_cols is 1
    if n_rows == 1 and n_cols == 1:
        axes = [axes]
    elif n_rows == 1 or n_cols == 1:
        axes = axes.flatten()
    else:
        axes = axes.flatten()

    print(f"Visualizing {len(selected_images)} randomly selected images from YOLO segmentation data")
    print(f"Images directory: {images_dir}")
    print(f"Labels directory: {labels_dir}")
    print("=" * 60)

    for idx, image_path in enumerate(selected_images):
        if idx >= total_plots:
            break

        # Load image
        image = cv2.imread(str(image_path))
        if image is None:
            print(f"Failed to load image: {image_path}")
            continue

        # Convert BGR to RGB
        image_rgb = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
        image_height, image_width = image_rgb.shape[:2]

        # Get corresponding label file
        label_file = labels_path / f"{image_path.stem}.txt"

        # Create a copy of the image for drawing masks
        masked_image = image_rgb.copy()

        # Check if label file exists and process it
        masks_drawn = 0
        if label_file.exists():
            try:
                with open(label_file, 'r') as f:
                    lines = f.readlines()

                for line_idx, line in enumerate(lines):
                    line = line.strip()
                    if not line:  # Skip empty lines (negative samples)
                        continue

                    parts = line.split()
                    if len(parts) < 7:  # Need at least class_id + 3 points (6 coordinates)
                        continue

                    try:
                        class_id = int(parts[0])
                        coords = [float(x) for x in parts[1:]]

                        # Convert normalized coordinates back to pixel coordinates
                        # YOLO format is x, y - coordinates are now fixed in label files
                        pixel_coords = []
                        for i in range(0, len(coords), 2):
                            if i + 1 < len(coords):
                                norm_x = coords[i]      # normalized x coordinate
                                norm_y = coords[i + 1]  # normalized y coordinate

                                # Convert to pixel coordinates (no swapping needed after fix)
                                x = int(norm_x * image_width)
                                y = int(norm_y * image_height)

                                # Store as [x, y] for OpenCV drawing functions
                                pixel_coords.append([x, y])

                        if len(pixel_coords) >= 3:  # Need at least 3 points for a polygon
                            # Draw filled polygon mask
                            polygon_points = np.array(pixel_coords, dtype=np.int32)

                            # Create mask overlay with transparency
                            overlay = masked_image.copy()
                            cv2.fillPoly(overlay, [polygon_points], (255, 0, 0))  # Red mask

                            # Apply transparency
                            alpha = 0.4
                            masked_image = cv2.addWeighted(masked_image, 1 - alpha, overlay, alpha, 0)

                            # Draw polygon outline
                            cv2.polylines(masked_image, [polygon_points], isClosed=True,
                                        color=(0, 255, 0), thickness=2)  # Green outline

                            # Draw vertices as small circles
                            for point in pixel_coords:
                                cv2.circle(masked_image, tuple(point), 3, (255, 255, 0), -1)  # Yellow dots

                            masks_drawn += 1

                    except (ValueError, IndexError) as e:
                        print(f"Error parsing line {line_idx + 1} in {label_file}: {e}")
                        continue

            except Exception as e:
                print(f"Error reading label file {label_file}: {e}")

        # Display the image with masks
        axes[idx].imshow(masked_image)

        # Create title with information
        title = f"{image_path.name}"
        if masks_drawn > 0:
            title += f"\n{masks_drawn} mask(s) drawn"
        else:
            title += "\nNo masks (negative sample)"

        axes[idx].set_title(title, fontsize=10)
        axes[idx].axis('off')

        print(f"  {idx + 1}. {image_path.name}: {masks_drawn} mask(s)")

    # Hide unused subplots
    for idx in range(len(selected_images), total_plots):
        axes[idx].axis('off')

    plt.tight_layout()
    plt.suptitle(f"YOLO Segmentation Training Data Visualization ({n_rows}x{n_cols})",
                 fontsize=14, y=0.98)
    plt.show()

    print("=" * 60)
    print("Legend:")
    print("  🔴 Red filled areas: Segmentation masks")
    print("  🟢 Green outlines: Polygon boundaries")
    print("  🟡 Yellow dots: Polygon vertices")


def fix_yolo_segmentation_coordinates(labels_dir, backup=True):
    """
    Fix YOLO segmentation label files by swapping x and y coordinates.

    Args:
        labels_dir (str): Path to directory containing YOLO segmentation labels
        backup (bool): Whether to create backup files before modification (default: True)

    Returns:
        dict: Statistics about the processing
    """
    from pathlib import Path
    import shutil

    labels_path = Path(labels_dir)

    if not labels_path.exists():
        print(f"Labels directory not found: {labels_dir}")
        return {}

    # Get all label files
    label_files = list(labels_path.glob("*.txt"))

    if len(label_files) == 0:
        print(f"No label files found in {labels_dir}")
        return {}

    stats = {
        'total_files': len(label_files),
        'processed_files': 0,
        'empty_files': 0,
        'error_files': 0,
        'total_polygons': 0,
        'errors': []
    }

    print(f"Fixing coordinates in {len(label_files)} label files...")
    print(f"Labels directory: {labels_dir}")
    print(f"Backup enabled: {backup}")
    print("=" * 60)

    for label_file in label_files:
        try:
            # Read original file
            with open(label_file, 'r') as f:
                lines = f.readlines()

            # Skip empty files
            if not lines or all(not line.strip() for line in lines):
                stats['empty_files'] += 1
                print(f"  Skipped (empty): {label_file.name}")
                continue

            # Create backup if requested
            if backup:
                backup_file = label_file.with_suffix('.txt.backup')
                shutil.copy2(label_file, backup_file)

            # Process each line
            fixed_lines = []
            polygons_in_file = 0

            for line_idx, line in enumerate(lines):
                line = line.strip()
                if not line:
                    fixed_lines.append(line + '\n')
                    continue

                parts = line.split()
                if len(parts) < 7:  # Need at least class_id + 3 points (6 coordinates)
                    fixed_lines.append(line + '\n')
                    continue

                try:
                    class_id = parts[0]
                    coords = [float(x) for x in parts[1:]]

                    # Swap x and y coordinates
                    swapped_coords = []
                    for i in range(0, len(coords), 2):
                        if i + 1 < len(coords):
                            x = coords[i]      # original x
                            y = coords[i + 1]  # original y
                            # Swap: put y first, then x
                            swapped_coords.extend([y, x])

                    # Create fixed line
                    coords_str = " ".join([f"{coord:.6f}" for coord in swapped_coords])
                    fixed_line = f"{class_id} {coords_str}\n"
                    fixed_lines.append(fixed_line)

                    polygons_in_file += 1

                except (ValueError, IndexError) as e:
                    error_msg = f"Error parsing line {line_idx + 1} in {label_file.name}: {e}"
                    stats['errors'].append(error_msg)
                    fixed_lines.append(line + '\n')  # Keep original line if parsing fails
                    continue

            # Write fixed file
            with open(label_file, 'w') as f:
                f.writelines(fixed_lines)

            stats['processed_files'] += 1
            stats['total_polygons'] += polygons_in_file
            print(f"  ✓ Fixed: {label_file.name} ({polygons_in_file} polygons)")

        except Exception as e:
            stats['error_files'] += 1
            error_msg = f"Error processing {label_file.name}: {str(e)}"
            stats['errors'].append(error_msg)
            print(f"  ✗ Error: {label_file.name} - {str(e)}")
            continue

    # Print summary
    print("\n" + "=" * 60)
    print("COORDINATE FIXING COMPLETE")
    print("=" * 60)
    print(f"Total files found: {stats['total_files']}")
    print(f"Successfully processed: {stats['processed_files']}")
    print(f"Empty files skipped: {stats['empty_files']}")
    print(f"Files with errors: {stats['error_files']}")
    print(f"Total polygons fixed: {stats['total_polygons']}")

    if stats['errors']:
        print(f"\nErrors encountered ({len(stats['errors'])}):")
        for error in stats['errors'][:5]:  # Show first 5 errors
            print(f"  - {error}")
        if len(stats['errors']) > 5:
            print(f"  ... and {len(stats['errors']) - 5} more errors")

    if backup:
        print(f"\nBackup files created with .backup extension")

    return stats


from pathlib import Path
import shutil
import numpy as np
import pandas as pd
from scipy.spatial import ConvexHull

def simplify_yolo_segmentation_masks(labels_dir, backup=True):
    """
    Simplify YOLO segmentation masks by applying convex hull to smooth the polygon.

    Args:
        labels_dir (str): Path to directory containing YOLO segmentation labels
        backup (bool): Whether to create backup files before modification (default: True)

    Returns:
        dict: Statistics about the processing
    """
    labels_path = Path(labels_dir)

    if not labels_path.exists():
        print(f"Labels directory not found: {labels_dir}")
        return {}

    label_files = list(labels_path.glob("*.txt"))

    if len(label_files) == 0:
        print(f"No label files found in {labels_dir}")
        return {}

    stats = {
        'total_files': len(label_files),
        'processed_files': 0,
        'empty_files': 0,
        'error_files': 0,
        'total_polygons': 0,
        'vertices_before': 0,
        'vertices_after': 0,
        'errors': []
    }

    print(f"Simplifying masks using Convex Hull in {len(label_files)} label files...")
    print(f"Labels directory: {labels_dir}")
    print(f"Backup enabled: {backup}")
    print("=" * 60)

    for label_file in label_files:
        try:
            with open(label_file, 'r') as f:
                lines = f.readlines()

            if not lines or all(not line.strip() for line in lines):
                stats['empty_files'] += 1
                print(f"  Skipped (empty): {label_file.name}")
                continue

            if backup:
                backup_file = label_file.with_suffix('.txt.simplified_backup')
                shutil.copy2(label_file, backup_file)

            simplified_lines = []
            polygons_in_file = 0

            for line_idx, line in enumerate(lines):
                line = line.strip()
                if not line:
                    simplified_lines.append('\n')
                    continue

                parts = line.split()
                if len(parts) < 7:
                    simplified_lines.append(line + '\n')
                    continue

                try:
                    class_id = parts[0]
                    coords = [float(x) for x in parts[1:]]

                    points = []
                    for i in range(0, len(coords), 2):
                        if i + 1 < len(coords):
                            points.append([coords[i], coords[i + 1]])

                    points = np.array(points)
                    original_vertex_count = len(points)
                    stats['vertices_before'] += original_vertex_count

                    # Apply convex hull if at least 3 points
                    if len(points) >= 3:
                        try:
                            hull = ConvexHull(points)
                            simplified_points = points[hull.vertices]
                        except Exception as hull_err:
                            stats['errors'].append(
                                f"Hull error in {label_file.name} line {line_idx + 1}: {hull_err}"
                            )
                            simplified_points = points  # fallback to original
                    else:
                        simplified_points = points

                    final_vertex_count = len(simplified_points)
                    stats['vertices_after'] += final_vertex_count

                    simplified_coords = simplified_points.flatten().tolist()
                    coords_str = " ".join([f"{c:.6f}" for c in simplified_coords])
                    simplified_line = f"{class_id} {coords_str}\n"
                    simplified_lines.append(simplified_line)
                    polygons_in_file += 1

                except (ValueError, IndexError) as e:
                    stats['errors'].append(
                        f"Parse error in {label_file.name} line {line_idx + 1}: {e}"
                    )
                    simplified_lines.append(line + '\n')
                    continue

            with open(label_file, 'w') as f:
                f.writelines(simplified_lines)

            stats['processed_files'] += 1
            stats['total_polygons'] += polygons_in_file
            print(f"  ✓ Simplified: {label_file.name} ({polygons_in_file} polygons)")

        except Exception as e:
            stats['error_files'] += 1
            stats['errors'].append(f"Error processing {label_file.name}: {str(e)}")
            print(f"  ✗ Error: {label_file.name} - {str(e)}")
            continue

    print("\n" + "=" * 60)
    print("MASK SIMPLIFICATION COMPLETE")
    print("=" * 60)
    print(f"Total files found: {stats['total_files']}")
    print(f"Successfully processed: {stats['processed_files']}")
    print(f"Empty files skipped: {stats['empty_files']}")
    print(f"Files with errors: {stats['error_files']}")
    print(f"Total polygons simplified: {stats['total_polygons']}")
    print(f"Vertices before: {stats['vertices_before']}")
    print(f"Vertices after: {stats['vertices_after']}")
    if stats['vertices_before'] > 0:
        reduction = (1 - stats['vertices_after'] / stats['vertices_before']) * 100
        print(f"Vertex reduction: {reduction:.1f}%")

    if stats['errors']:
        print(f"\nErrors encountered ({len(stats['errors'])}):")
        for error in stats['errors'][:5]:
            print(f"  - {error}")
        if len(stats['errors']) > 5:
            print(f"  ... and {len(stats['errors']) - 5} more errors")

    if backup:
        print(f"\nBackup files created with .simplified_backup extension")

    return stats

if __name__ == "__main__":
    # Example usage when running this file directly
    print("Image Display Utilities")
    print("======================")
    print("This module provides functions for displaying full and initial images side by side.")
    print("And preparing YOLO keypoint detection training data.")
    print("\nMain functions:")
    print("  - show_full_and_initial_images(jpg_files, init_files, fold_name, num_pairs, label_df)")
    print("  - prepare_yolo_keypoint_training_data(jpg_files, init_files, label_df, output_base_dir, negative_sample_prepare, number_of_negative_sampling)")
    print("  - prepare_yolo_segmentation_training_data(jpg_files, init_files, label_df, output_base_dir, negative_sample_prepare, number_of_negative_sampling)")
    print("  - visualize_yolo_segmentation_data(images_dir, labels_dir, n_rows, n_cols)")
    print("  - fix_yolo_segmentation_coordinates(labels_dir, backup)")
    print("  - simplify_yolo_segmentation_masks(labels_dir, max_vertices, backup)")
    print("  - process_image_with_hulls(process_image, is_middle_point, distance_threshold, offset_x)")
    print("  - process_image_with_convex_hulls(process_image, is_middle_point, distance_threshold, offset_x)")
    print("\nExample usage:")
    print("  from image_display_utils import prepare_yolo_segmentation_training_data, fix_yolo_segmentation_coordinates, simplify_yolo_segmentation_masks")
    print("  # 1. Prepare segmentation data")
    print("  seg_stats = prepare_yolo_segmentation_training_data(jpg_files, init_files, label_df, output_base_dir='./yolo_seg_data')")
    print("  # 2. Fix coordinate alignment")
    print("  fix_stats = fix_yolo_segmentation_coordinates('./yolo_seg_data/labels/train')")
    print("  # 3. Simplify masks for better training")
    print("  simplify_stats = simplify_yolo_segmentation_masks('./yolo_seg_data/labels/train', max_vertices=8)")
    print("  # 4. Visualize results")
    print("  visualize_yolo_segmentation_data('./yolo_seg_data/images/train', './yolo_seg_data/labels/train', n_rows=2, n_cols=3)")
