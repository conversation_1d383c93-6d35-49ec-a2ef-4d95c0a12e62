import os
from glob import glob
import pandas as pd

"""
Prepare YOLO structure

images
- train
- validation
labels
- train
- validation
"""

LABEL_POOL = "../data/process_data/[SNL]_data/for_training/"
LABEL_DESTINATION = "../data/process_data/[SNL]_data/labels_pool"

import os
import shutil

def extract_and_move_labels(label_pool_dir, destination_base_dir):
    """
    Extracts 'move.txt' files from 'SNL_fold_X_label/obj_train_data/'
    and moves them to a specified destination.

    Args:
        label_pool_dir (str): The path to the main LABEL_POOL directory.
        destination_base_dir (str): The base path for the destination,
                                    e.g., '../data/process_data/'.
    """
    
    relative_destination_path = "[SNL]_data/labels_pool" 
    full_destination_path = os.path.join(destination_base_dir, relative_destination_path)

    os.makedirs(full_destination_path, exist_ok=True)
    print(f"Ensured destination directory exists: {full_destination_path}")

    # Iterate through the 14 folds
    for i in range(1, 15): # Loop from 1 to 14
        folder_name = f"SNL_fold_{i}_label"
        source_obj_train_data_path = os.path.join(label_pool_dir, folder_name, "obj_train_data")
        
        if os.path.exists(full_destination_path):
            all_files = os.listdir(source_obj_train_data_path)
            for file in all_files:
                source_path = os.path.join(source_obj_train_data_path, file)
                shutil.copy(source_path, full_destination_path)
destination_base_directory = "../data/process_data" 


extract_and_move_labels(LABEL_POOL, destination_base_directory)
print("\nProcess completed.")

IMAGE_POOLS = "../data/process_data/[SNL]_data/snl_image_pool"
LABEL_POOLS = "../data/process_data/[SNL]_data/labels_pool"

from snl_data_extraction import extract_label_files_from_label_pool, check_positive_negative_samples, print_sample_statistics, analyze_class_instances, plot_positive_negative_distribution, plot_class_distribution, print_class_statistics

sample_stats = check_positive_negative_samples()

plot_positive_negative_distribution(sample_stats)

# Analyze positive/negative samples and plot
class_stats = analyze_class_instances()

plot_class_distribution(class_stats)

print_class_statistics(class_stats)

from snl_data_extraction import remove_number_prefix_from_filenames

stats = remove_number_prefix_from_filenames(
    "../data/process_data/[SNL]_data/snl_image_pool"
    , dry_run=False)
# print_rename_statistics(stats)
# stats

from snl_data_extraction import filter_top_defect_d_labels

move_stats = filter_top_defect_d_labels(
    "../data/process_data/[SNL]_data/labels_pool",
    "test",
    dry_run=True
)
# print_filter_statistics(move_stats)

move_stat=filter_top_defect_d_labels(
    "../data/process_data/[SNL]_data/labels_pool",
    "test",
    top_percentage=20,
    dry_run=False
)
# print_filter_statistics(move_stat)

labels_dir = "../data/process_data/[SNL]_data/labels_no_top_D"
sample_stats = check_positive_negative_samples(labels_dir)
class_stats = analyze_class_instances(labels_dir)

plot_positive_negative_distribution(sample_stats)

plot_class_distribution(class_stats)

# PREPARE TRAINING INFORMATION
from snl_data_extraction import prepare_training_data

# stats = prepare_training_data(
#     labels_dir="../data/process_data/[SNL]_data/labels_no_top_D",
#     image_pools_dir="../data/process_data/[SNL]_data/snl_image_pool/",
#     output_dir="../data/process_data/[SNL]_data/data",
#     validation_split=0.1,
#     include_negative_samples=True,
#     negative_samples_percent=20,
#     dry_run=False
# )

# stats = prepare_training_data(
#     labels_dir="../data/process_data/[SNL]_data/labels_no_top_D",
#     image_pools_dir="../data/process_data/[SNL]_data/snl_image_pool/",
#     output_dir="../data/process_data/[SNL]_data/data",
#     validation_split=0.1,
#     include_negative_samples=True,
#     negative_samples_percent=10,
#     is_preprocess_image=True,
#     dry_run=False
# )

prepare_training_data(
    labels_dir="../data/process_data/[SNL]_data/labels_no_top_D",
    image_pools_dir="../data/process_data/[SNL]_data/snl_image_pool/",
    output_dir="../data/process_data/[SNL]_data/remove_green_expand_width/",
    is_preprocess_image=True,
    is_disable_highlight=True,  # No polylines
    is_detected_focus=False,     # All classes -> 0
    use_binary_mask=False,
    remove_green=True,
    include_negative_samples=True,
    negative_samples_percent=10,
    expand_width=True,
    dry_run=False
)

# PLOT FIRST PEAK
import cv2
from glob import glob
import os
from snl_data_extraction import preprocess_sonic_logging
import numpy as np
from matplotlib import pyplot as plt



import os
import cv2
import numpy as np
import matplotlib.pyplot as plt
import random
from typing import List, Tuple

def plot_white_mask(image_dir, label_dir=None, num_samples=3):
    image_names = os.listdir(image_dir)
    selected_images = random.sample(image_names, min(num_samples, len(image_names)))

    fig, axes = plt.subplots(num_samples, 3, figsize=(15, 5 * num_samples))
    axes = axes if isinstance(axes, np.ndarray) else np.array([axes])
    axes = axes.reshape((num_samples, 3))

    for i, image_name in enumerate(selected_images):
        # === Load image ===
        filename = os.path.join(image_dir, image_name)
        label_name = os.path.join(label_dir, image_name.replace('.jpg', '.txt'))
        image = cv2.imread(filename)
        rgb_image = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)

        # === Preprocess and get overlay ===
        overlay_image, peaks_xy = preprocess_sonic_logging(rgb_image, disable_highlight=False, draw_highlighting=True)

        # 1. Invert and extract grayscale
        inverted_overlay = 255 - overlay_image
        h, w = inverted_overlay.shape[:2]

        # 2. Append bottom-right and top-right corner to close polygon
        peaks_xy = np.append(peaks_xy, [[w - 10, h - 1], [w - 10, 0]], axis=0)
        polygon_array = np.array(peaks_xy, dtype=np.int32).reshape((-1, 1, 2))

        # 3. Create dark frame and fill the white polygon
        dark_frame = np.zeros_like(inverted_overlay)
        cv2.fillPoly(dark_frame, [polygon_array], color=(255, 255, 255), lineType=cv2.LINE_8)

        # 4. === Draw YOLO bounding boxes (if label file exists) ===
        if os.path.exists(label_name):
            with open(label_name, 'r') as f:
                for line in f:
                    parts = line.strip().split()
                    if len(parts) != 5:
                        continue
                    cls_id, x_center, y_center, box_w, box_h = map(float, parts)

                    # Convert YOLO normalized coords to pixel coords
                    x_center *= w
                    y_center *= h
                    box_w *= w
                    box_h *= h

                    x1 = int(x_center - box_w / 2)
                    y1 = int(y_center - box_h / 2)
                    x2 = int(x_center + box_w / 2)
                    y2 = int(y_center + box_h / 2)

                    # Draw rectangle on dark_frame in red
                    cv2.rectangle(dark_frame, (x1, y1), (x2, y2), (255, 0, 0), 5)
                    # Optionally draw class label
                    # cv2.putText(dark_frame, str(int(cls_id)), (x1, y1 - 5), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 0, 255), 1)

        for x, y in peaks_xy:
            cv2.circle(rgb_image, (int(x), int(y)), 5, (255, 0, 0), -1)

        # === Plotting ===
        axs = axes[i]
        axs[0].imshow(rgb_image)
        axs[0].set_title(f"Original: {image_name}")
        axs[0].axis("off")

        axs[1].imshow(overlay_image)
        axs[1].set_title("Preprocessed Overlay")
        axs[1].axis("off")

        axs[2].imshow(dark_frame)
        axs[2].set_title("Mask + YOLO Box")
        axs[2].axis("off")

    plt.tight_layout()
    plt.show()


import os
# SAMPLE_FOLDER = glob("../data/process_data/[SNL]_data/data/images/train/*.jpg")
IMAGE_NAME = os.listdir("../data/process_data/[SNL]_data/snl_image_pool")
filename = os.path.join("../data/process_data/[SNL]_data/snl_image_pool", IMAGE_NAME[1])
sample_image = cv2.imread(filename)
rgb_image = cv2.cvtColor(sample_image, cv2.COLOR_BGR2RGB)
label_filename = filename.replace(".jpg", ".txt").split("/")[-1]
label_path = os.path.join("../data/process_data/[SNL]_data/labels_pool", label_filename)

# IMAGE 15 = DEFECT

import cv2
import numpy as np
from typing import List, Tuple

def extract_near_black_pixels(rgb_image: np.ndarray, max_black_threshold: int = 150, skip_left_percent: float = 0.1) -> List[Tuple[int, int]]:
    """
    Extract all pixels that are nearly black (intensity close to 0) from an RGB rgb_image,
    skipping the leftmost portion of the rgb_image (e.g., axis labels).
    Returns:
        List[Tuple[int, int]]: List of (y, x) coordinates where grayscale intensity <= threshold.
    """
    gray = cv2.cvtColor(rgb_image, cv2.COLOR_RGB2GRAY)

    height, width = gray.shape
    skip_x = int(width * skip_left_percent)

    black_mask = (gray <= max_black_threshold)
    black_mask[:, :skip_x] = 0  # Zero-out the left 10%
    y_coords, x_coords = np.where(black_mask)
    peaks = list(zip(y_coords, x_coords))

    return peaks

import numpy as np
import cv2
from typing import List, Tuple

def vision_representation_featured(rgb_image: np.ndarray, intensity_threshold: int = 120) -> Tuple[np.ndarray, List[Tuple[int, int]]]:
    """
    Extract the foreground (peak) pixels from an RGB image based on near-black pixel detection.
    Pixels not part of the peaks are set to white.

    Args:
        rgb_image (np.ndarray): Original RGB image (H, W, 3).
        intensity_threshold (int): Grayscale intensity threshold to define near-black regions.

    Returns:
        Tuple[np.ndarray, List[Tuple[int, int]]]: 
            - Modified RGB image (non-peak pixels turned white),
            - List of (y, x) peak coordinates.
    """
    # Extract peak coordinates where pixel is nearly black
    peaks = extract_near_black_pixels(rgb_image, max_black_threshold=intensity_threshold)

    # Start with a white canvas
    foreground_image = np.ones_like(rgb_image, dtype=np.uint8) * 255

    # Fill in original values only at peak positions
    for y, x in peaks:
        foreground_image[y, x] = rgb_image[y, x]

    return foreground_image, peaks


def draw_yolo_bboxes(image: np.ndarray, label_path: str, color: Tuple[int, int, int] = (255, 0, 0), thickness: int = 2) -> np.ndarray:
    """
    Draw YOLO-format bounding boxes on an image.

    Args:
        image (np.ndarray): RGB image as a NumPy array.
        label_path (str): Path to the YOLO label file (each line: class x_center y_center width height, normalized).
        color (Tuple[int, int, int]): Color of the bounding boxes (default: red).
        thickness (int): Thickness of bounding box lines.

    Returns:
        np.ndarray: Image with bounding boxes drawn.
    """
    h, w = image.shape[:2]

    # Make a copy to avoid modifying original image
    image_with_boxes = image.copy()

    try:
        with open(label_path, "r") as f:
            for line in f:
                parts = line.strip().split()
                if len(parts) != 5:
                    continue  # skip invalid lines
                cls, x_center, y_center, box_width, box_height = map(float, parts)

                # Convert normalized YOLO to pixel coordinates
                x_center *= w
                y_center *= h
                box_width *= w
                box_height *= h

                x1 = int(x_center - box_width / 2)
                y1 = int(y_center - box_height / 2)
                x2 = int(x_center + box_width / 2)
                y2 = int(y_center + box_height / 2)

                # Draw rectangle
                cv2.rectangle(image_with_boxes, (x1, y1), (x2, y2), color, thickness)

                # Optionally, label with class ID
                cv2.putText(image_with_boxes, f"{int(cls)}", (x1, y1 - 5),
                            cv2.FONT_HERSHEY_SIMPLEX, 3, color, 5)
    except FileNotFoundError:
        print(f"[Warning] Label file not found: {label_path}")

    return image_with_boxes


# CALLING FUNCTION

vision_representation, peaks = vision_representation_featured(rgb_image, intensity_threshold=180)
overlay_feature_representation,_ = preprocess_sonic_logging(rgb_image, disable_highlight=False)
# three_channel_image = np.stack([vision_representation] * 3, axis=-1)
label_image = draw_yolo_bboxes(vision_representation, label_path, color=(255, 0, 0), thickness=5)
original_image = draw_yolo_bboxes(rgb_image, label_path, color=(255, 0, 0), thickness=5)
overlay_image = draw_yolo_bboxes(overlay_feature_representation, label_path, color=(255, 0, 0), thickness=5)

# plt.imshow(label_image)

# PLOT ORIGINAL WITH GRAY IMAGE
fig, axs = plt.subplots(1, 3, figsize=(20, 10))
axs[0].imshow(original_image)
axs[0].set_title("Original")
axs[0].axis("off")

axs[1].imshow(label_image, cmap='gray')
axs[1].set_title("Grayscale")
axs[1].axis("off")

axs[2].imshow(overlay_image)
axs[2].set_title("Overlay")
axs[2].axis("off")

import os
import cv2
import numpy as np
import matplotlib.pyplot as plt
import random

def compare_sample(image_dir, label_dir, n_sample=5):
    """
    Randomly select `n_sample` images from `image_dir` and plot:
    - Original image + YOLO bounding boxes
    - Overlay feature representation + bounding boxes
    - Visual foreground representation + bounding boxes
    """

    image_names = [f for f in os.listdir(image_dir) if f.endswith('.jpg')]
    selected_images = random.sample(image_names, min(n_sample, len(image_names)))

    fig, axes = plt.subplots(n_sample, 3, figsize=(18, 6 * n_sample))
    if n_sample == 1:
        axes = np.expand_dims(axes, axis=0)

    for i, image_name in enumerate(selected_images):
        image_path = os.path.join(image_dir, image_name)
        label_path = os.path.join(label_dir, image_name.replace('.jpg', '.txt'))

        # === Load image and prepare RGB ===
        image = cv2.imread(image_path)
        rgb_image = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)

        # === Preprocess visual and overlay representations ===
        visual_representation, _ = vision_representation_featured(rgb_image, intensity_threshold=150)
        overlay_feature_representation, _ = preprocess_sonic_logging(rgb_image, disable_highlight=False)

        # === Draw YOLO bounding boxes on all 3 representations ===
        labeled_original = draw_yolo_bboxes(rgb_image.copy(), label_path, color=(255, 0, 0), thickness=5)
        labeled_overlay = draw_yolo_bboxes(overlay_feature_representation.copy(), label_path, color=(255, 0, 0), thickness=5)
        labeled_visual = draw_yolo_bboxes(visual_representation.copy(), label_path, color=(255, 0, 0), thickness=5)

        # === Plotting ===
        axs = axes[i]
        axs[0].imshow(labeled_original)
        axs[0].set_title(f"Original with Labels\n{image_name}")
        axs[0].axis("off")

        axs[1].imshow(labeled_overlay)
        axs[1].set_title("Overlay + Labels")
        axs[1].axis("off")

        axs[2].imshow(labeled_visual)
        axs[2].set_title("Foreground Peaks + Labels")
        axs[2].axis("off")

    plt.tight_layout()
    plt.show()


compare_sample(
    "../data/process_data/[SNL]_data/snl_image_pool",
    "../data/process_data/[SNL]_data/labels_pool",
    n_sample=5
)

"""
HEADING TO NEW DIRECTION : BY USING SEGMENTATION APPROACH
1. How to map signal contours for from each bounding box to segmentation ( pixel wise ) labels ?
Peaks handle
- Use the preprocess information from foreground peaks (black + white with green removal )

"""

# SAMPLE_FOLDER = glob("../data/process_data/[SNL]_data/data/images/train/*.jpg")
IMAGE_NAME = os.listdir("../data/process_data/[SNL]_data/snl_image_pool")
filename = os.path.join("../data/process_data/[SNL]_data/snl_image_pool", IMAGE_NAME[5000])
sample_image = cv2.imread(filename)
rgb_image = cv2.cvtColor(sample_image, cv2.COLOR_BGR2RGB)
label_filename = filename.replace(".jpg", ".txt").split("/")[-1]
label_path = os.path.join("../data/process_data/[SNL]_data/labels_pool", label_filename)

# IMAGE 15, 300 = DEFECT

draw_frame = rgb_image.copy()
vision_representation, _ = vision_representation_featured(rgb_image, intensity_threshold=150)
draw_frame = draw_yolo_bboxes(vision_representation.copy(), label_path, color=(255, 0, 0), thickness=5)
plt.imshow(draw_frame)

import cv2
import numpy as np

def extract_rois_from_yolo_labels(image: np.ndarray, label_path: str):
    """
    Extract regions of interest (ROIs) from an image based on YOLO format label file.

    Args:
        image (np.ndarray): The original image (H, W) or (H, W, C).
        label_path (str): Path to the .txt label file (YOLO format).

    Returns:
        List[Tuple[int, int, int, int]], List[np.ndarray]: 
            A list of (x, y, w, h) bounding box coordinates (in pixel),
            and corresponding cropped image regions (ROIs).
    """
    height, width = image.shape[:2]
    rois = []
    boxes = []
    classes = []

    with open(label_path, 'r') as f:
        lines = f.readlines()

    for line in lines:
        parts = line.strip().split()
        if len(parts) != 5:
            continue  # skip invalid lines

        cls_id, x_c, y_c, w, h = map(float, parts)
        # Convert normalized to pixel values
        x_c *= width
        y_c *= height
        w *= width
        h *= height

        x1 = int(max(x_c - w / 2, 0))
        y1 = int(max(y_c - h / 2, 0))
        x2 = int(min(x_c + w / 2, width))
        y2 = int(min(y_c + h / 2, height))

        roi = image[y1:y2, x1:x2]
        rois.append(roi)
        boxes.append((x1, y1, x2 - x1, y2 - y1))
        classes.append(cls_id)

    return boxes, rois, classes

boxes, rois, classes = extract_rois_from_yolo_labels(vision_representation, label_path)

import numpy as np
import cv2
from typing import List, Tuple

def create_segmentation_masks(rois: List[np.ndarray],
                               classes: List[int],
                               boxes: List[Tuple[int, int, int, int]],
                               fatness_factor: float = 0.8,
                               spike_position: float = 0.1,
                               tip_height_ratio: float = 0.2
                               ) -> Tuple[List[np.ndarray], List[List[Tuple[int, int]]]]:
    """
    Generate segmentation masks and polygon coordinates.

    For h >= 50, build polygon from first white pixel per row, and explicitly close the loop.
    """
    masks = []
    abs_positions = []

    for roi, cls, (x0, y0, w, h) in zip(rois, classes, boxes):
        mask = np.zeros((h, w), dtype=np.uint8)

        if h < 50 and cls != 2:
            # --- Spike Mask ---
            base_start = int(h * spike_position)
            base_end = int(h * (1 - spike_position))
            tip_height = max(1, int(h * tip_height_ratio))
            tip_start = (base_start + base_end) // 2 - tip_height // 2
            tip_end = tip_start + tip_height

            spike_pts = []
            for y in range(base_start, base_end + 1):
                norm = 1 - abs(y - (base_start + base_end) / 2) / ((base_end - base_start) / 2)
                line_width = int(norm * w * fatness_factor)
                if tip_start <= y <= tip_end:
                    line_width = w

                for x in range(line_width):
                    mask[y, x] = 255
                    spike_pts.append((x0 + x, y0 + y))

            masks.append(mask)
            abs_positions.append(spike_pts)

        else:
            # --- Polygon Mask ---
            lap = cv2.Laplacian(roi, cv2.CV_64F)
            lap = np.uint8(np.absolute(lap))
            _, binary = cv2.threshold(lap, 30, 255, cv2.THRESH_BINARY)

            # Left edge from first white per row
            left_edge = []
            left_edge.append((0, 0))
            for y in range(h):
                row = binary[y]
                white_indices = np.where(row > 0)[0]
                x = white_indices[0] if len(white_indices) > 0 else w - 1
                left_edge.append((x, y))

            # Ensure polygon closes by explicitly wrapping around to form area
            if len(left_edge) > 2:
                polygon = left_edge.copy()
                # polygon.append((w - 1, h - 1))  # bottom-right corner
                # polygon.append((w - 1, 0))      # top-right corner
                polygon.append((0, h-1)) # Bottom left corner
                polygon.append(left_edge[0])    # back to first point

                polygon_np = np.array(polygon, dtype=np.int32)
                cv2.fillPoly(mask, [polygon_np], 255)
                kernel = np.ones((1, 7), np.uint8)
                mask = cv2.dilate(mask, kernel, iterations=1)

                # Convert to original image coordinates
                abs_xy = [(x0 + x, y0 + y) for (x, y) in polygon]
            else:
                abs_xy = []

            masks.append(mask)
            abs_positions.append(abs_xy)

    return masks, abs_positions

label_masks, absolute_positions = create_segmentation_masks(rois, classes, boxes, fatness_factor=0.6, spike_position=0.3)

import numpy as np
import cv2
from typing import List, Tuple

def overlay_polygons_on_image(image: np.ndarray,
                               polygons: List[List[Tuple[int, int]]],
                               color=(0, 0, 255),
                               alpha=0.5) -> np.ndarray:
    """
    Overlay all polygonal masks on the image in a single pass to avoid darkened overlays.

    Args:
        image (np.ndarray): Original image (H, W, 3).
        polygons (list of list of (x, y)): List of absolute polygon coordinates.
        color (tuple): BGR color of the polygon fill.
        alpha (float): Transparency of the overlay (0.0–1.0).

    Returns:
        np.ndarray: Image with overlaid polygons.
    """
    overlay_mask = np.zeros_like(image, dtype=np.uint8)

    for polygon_points in polygons:
        if len(polygon_points) < 3:
            continue
        pts = np.array(polygon_points, dtype=np.int32).reshape((-1, 1, 2))
        cv2.fillPoly(overlay_mask, [pts], color)

    # Blend once with the original image
    blended = cv2.addWeighted(overlay_mask, alpha, image, 1 - alpha, 0)

    return blended


overlay_image = overlay_polygons_on_image(rgb_image, absolute_positions, color=(255,0 ,0), alpha=0.7)
# Enhanced original
h, s, v = cv2.split(cv2.cvtColor(rgb_image, cv2.COLOR_RGB2HSV))
# v = cv2.equalizeHist(v)
# equalized_rgb_image = cv2.cvtColor(cv2.merge((h, s, v)), cv2.COLOR_HSV2RGB)
# r, g, b  = cv2.split(rgb_image)
# equalized_rgb_image = cv2.merge(r , cv2.equalizeHist(g), cv2.equalizeHist(b)))

# PLOT WITH ORIGINAL
fig, axs = plt.subplots(1, 3, figsize=(12, 6))
axs[0].imshow(rgb_image)
axs[0].set_title("Original Image")
axs[0].axis("off")


# PREPROCESS DRAW FRAME, ERODE TO MAKE A GAP MORE APART
# draw_r, draw_g, draw_b = cv2.split(draw_frame)
# kernel = np.ones((3, 3), np.uint8)
# draw_g = cv2.erode(draw_g, kernel, iterations=1)
# draw_frame = cv2.merge((draw_r, draw_g, draw_b))
# Optional: Dilate horizontally only to preserve width
# kernel = np.ones((1, 7), np.uint8)
# dilated = cv2.dilate(draw_g, kernel, iterations=1)

# PLOT WITH YOLO LABEL
axs[1].imshow(draw_frame)
axs[1].set_title("Defect bounding boxes")
axs[1].axis("off")


axs[2].imshow(overlay_image)
axs[2].set_title("Image with Overlaid Polygons")
axs[2].axis("off")

# save 
# resize and save 
resized_overlay_image = cv2.resize(overlay_image, (640, 640))
cv2.imwrite("resize_overlay_image.jpg", resized_overlay_image)

plt.tight_layout()
plt.show()


# ASSEMBLE FEATURED

def extract_near_black_pixels(rgb_image: np.ndarray, max_black_threshold: int = 150, skip_left_percent: float = 0.1) -> List[Tuple[int, int]]:
    """
    Extract all pixels that are nearly black (intensity close to 0) from an RGB rgb_image,
    skipping the leftmost portion of the rgb_image (e.g., axis labels).
    Returns:
        List[Tuple[int, int]]: List of (y, x) coordinates where grayscale intensity <= threshold.
    """
    gray = cv2.cvtColor(rgb_image, cv2.COLOR_RGB2GRAY)
    height, width = gray.shape
    skip_x = int(width * skip_left_percent)
    black_mask = (gray <= max_black_threshold)
    black_mask[:, :skip_x] = 0  # Zero-out the left 10%
    y_coords, x_coords = np.where(black_mask)
    peaks = list(zip(y_coords, x_coords))

    return peaks

def vision_representation_featured(rgb_image: np.ndarray, intensity_threshold: int = 120) -> Tuple[np.ndarray, List[Tuple[int, int]]]:
    """
    Extract the foreground (peak) pixels from an RGB image based on near-black pixel detection.
    Pixels not part of the peaks are set to white.
    """
    peaks = extract_near_black_pixels(rgb_image, max_black_threshold=intensity_threshold)
    foreground_image = np.ones_like(rgb_image, dtype=np.uint8) * 255
    for y, x in peaks:
        foreground_image[y, x] = rgb_image[y, x]

    return foreground_image, peaks

def extract_rois_from_yolo_labels(image: np.ndarray, label_path: str):
    """
    Extract regions of interest (ROIs) from an image based on YOLO format label file.
    Returns:
        List[Tuple[int, int, int, int]], List[np.ndarray]: 
            A list of (x, y, w, h) bounding box coordinates (in pixel),
            and corresponding cropped image regions (ROIs).
    """
    height, width = image.shape[:2]
    rois = []
    boxes = []
    classes = []
    with open(label_path, 'r') as f:
        lines = f.readlines()
    for line in lines:
        parts = line.strip().split()
        if len(parts) != 5:
            continue  # skip invalid lines

        cls_id, x_c, y_c, w, h = map(float, parts)
        # Convert normalized to pixel values
        x_c *= width
        y_c *= height
        w *= width
        h *= height

        x1 = int(max(x_c - w / 2, 0))
        y1 = int(max(y_c - h / 2, 0))
        x2 = int(min(x_c + w / 2, width))
        y2 = int(min(y_c + h / 2, height))

        roi = image[y1:y2, x1:x2]
        rois.append(roi)
        boxes.append((x1, y1, x2 - x1, y2 - y1))
        classes.append(cls_id)

    return boxes, rois, classes

def create_segmentation_masks(rois: List[np.ndarray],
                               classes: List[int],
                               boxes: List[Tuple[int, int, int, int]],
                               fatness_factor: float = 0.8,
                               spike_position: float = 0.1,
                               tip_height_ratio: float = 0.2
                               ) -> Tuple[List[np.ndarray], List[List[Tuple[int, int]]]]:
    """
    Generate segmentation masks and polygon coordinates.

    For h >= 50, build polygon from first white pixel per row, and explicitly close the loop.
    """
    masks = []
    abs_positions = []

    for roi, cls, (x0, y0, w, h) in zip(rois, classes, boxes):
        mask = np.zeros((h, w), dtype=np.uint8)

        if h < 50 and cls != 2:
            # --- Spike Mask ---
            base_start = int(h * spike_position)
            base_end = int(h * (1 - spike_position))
            tip_height = max(1, int(h * tip_height_ratio))
            tip_start = (base_start + base_end) // 2 - tip_height // 2
            tip_end = tip_start + tip_height

            spike_pts = []
            for y in range(base_start, base_end + 1):
                norm = 1 - abs(y - (base_start + base_end) / 2) / ((base_end - base_start) / 2)
                line_width = int(norm * w * fatness_factor)
                if tip_start <= y <= tip_end:
                    line_width = w

                for x in range(line_width):
                    mask[y, x] = 255
                    spike_pts.append((x0 + x, y0 + y))

            masks.append(mask)
            abs_positions.append(spike_pts)

        else:
            # --- Polygon Mask ---
            lap = cv2.Laplacian(roi, cv2.CV_64F)
            lap = np.uint8(np.absolute(lap))
            _, binary = cv2.threshold(lap, 30, 255, cv2.THRESH_BINARY)

            # Left edge from first white per row
            left_edge = []
            left_edge.append((0, 0))
            for y in range(h):
                row = binary[y]
                white_indices = np.where(row > 0)[0]
                x = white_indices[0] if len(white_indices) > 0 else w - 1
                left_edge.append((x, y))
            if len(left_edge) > 2:
                polygon = left_edge.copy()
                polygon.append((0, h-1)) # Bottom left corner
                polygon.append(left_edge[0])    # back to first point

                polygon_np = np.array(polygon, dtype=np.int32)
                cv2.fillPoly(mask, [polygon_np], 255)
                kernel = np.ones((1, 7), np.uint8)
                mask = cv2.dilate(mask, kernel, iterations=1)

                # Convert to original image coordinates
                abs_xy = [(x0 + x, y0 + y) for (x, y) in polygon]
            else:
                abs_xy = []

            masks.append(mask)
            abs_positions.append(abs_xy)

    return masks, abs_positions

def overlay_polygons_on_image(image: np.ndarray,
                               polygons: List[List[Tuple[int, int]]],
                               color=(0, 0, 255),
                               alpha=0.5) -> np.ndarray:
    """
    Overlay all polygonal masks on the image in a single pass to avoid darkened overlays.
    """
    overlay_mask = np.zeros_like(image, dtype=np.uint8)

    for polygon_points in polygons:
        if len(polygon_points) < 3:
            continue
        pts = np.array(polygon_points, dtype=np.int32).reshape((-1, 1, 2))
        cv2.fillPoly(overlay_mask, [pts], color)

    # Blend once with the original image
    blended = cv2.addWeighted(overlay_mask, alpha, image, 1 - alpha, 0)

    return blended


def prepare_yolo_masks(rgb_image, label_path, is_return_image=False):
    process_frame = rgb_image.copy()
    vision_representation_bw, _ = vision_representation_featured(process_frame, intensity_threshold=150)
    boxes, rois, classes = extract_rois_from_yolo_labels(vision_representation_bw, label_path)
    label_masks, absolute_position = create_segmentation_masks(rois, classes, boxes, fatness_factor=0.6, spike_position=0.15)
    overlay_image = overlay_polygons_on_image(rgb_image, absolute_position, color=(255,0 ,0), alpha=0.7)

    if is_return_image:
        return absolute_position, overlay_image
    return absolute_position


def random_sampling_plot_segment(image_dir, label_dir, n_sample=5):
    """"""
    image_names = os.listdir(image_dir)
    selected_images = random.sample(image_names, min(n_sample, len(image_names)))

    fig, axes = plt.subplots(n_sample, 3, figsize=(15, 5 * n_sample))

    for i, image_name in enumerate(selected_images):
        filename = os.path.join(image_dir, image_name)
        image = cv2.imread(filename)
        rgb_image = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
        label_filename = filename.replace(".jpg", ".txt").split("/")[-1]
        label_path = os.path.join(label_dir, label_filename)
        groundtruth_image = draw_yolo_bboxes(rgb_image.copy(), label_path, color=(255, 0, 0), thickness=5)


        absolute_position, overlay_image = prepare_yolo_masks(rgb_image, label_path, is_return_image=True)

        axs = axes[i] if n_sample > 1 else axes
        axs[0].imshow(rgb_image)
        axs[0].set_title(f"Original: {image_name}")
        axs[0].axis("off")

        axs[1].imshow(groundtruth_image)
        axs[1].set_title("Groundtruth image")
        axs[1].axis("off")

        axs[2].imshow(overlay_image)
        axs[2].set_title("Segmentation mask")
        axs[2].axis("off")

    plt.tight_layout()
    plt.show()

random_sampling_plot_segment(
    "../data/process_data/[SNL]_data/snl_image_pool",
    "../data/process_data/[SNL]_data/labels_pool",
    n_sample=5
)

from snl_data_extraction import prepare_segmentation_data

# prepare_training_data(
#     labels_dir="../data/process_data/[SNL]_data/labels_no_top_D",
#     image_pools_dir="../data/process_data/[SNL]_data/snl_image_pool/",
#     output_dir="../data/process_data/[SNL]_data/remove_green_expand_width/",
#     is_preprocess_image=True,
#     is_disable_highlight=True,  # No polylines
#     is_detected_focus=False,     # All classes -> 0
#     use_binary_mask=False,
#     remove_green=True,
#     include_negative_samples=True,
#     negative_samples_percent=10,
#     expand_width=True,
#     dry_run=False
# )


prepare_segmentation_data(
    labels_dir="../data/process_data/[SNL]_data/labels_no_top_D",
    image_pools_dir="../data/process_data/[SNL]_data/snl_image_pool/",
    output_dir="../data/process_data/[SNL]_data/segmentation_prepared",
    include_negative_samples=True,
    negative_samples_percent=5,
    sampling_ratio=0.3,
    seed=42,
    dry_run=False
)

import os
from glob import glob
# PREDICTION
TEST_SET_IMAGE = "../data/process_data/[SNL]_data/test_set/test_set_image"
TEST_SET_LABEL = "../data/process_data/[SNL]_data/test_set/test_set_label/obj_train_data"


image_filenames = os.listdir(TEST_SET_IMAGE)
image_filename = image_filenames[10]
image_path = os.path.join(TEST_SET_IMAGE, image_filename)
label_path = os.path.join(TEST_SET_LABEL, image_filename.replace(".jpg", ".txt"))


from ultralytics import YOLO
trained_model = YOLO("../models/weights/yolo_m_expand_width.pt")


rgb_image = cv2.cvtColor(cv2.imread(image_path), cv2.COLOR_BGR2RGB)
draw_frame = draw_yolo_bboxes(rgb_image, label_path)

results = trained_model.predict(rgb_image, task='detect', verbose=False)
# results = trained_model.predict(TEST_SET_IMAGE, task='detect', verbose=False)
# results[0].show()
map_class = {0 : "defect_B", 1: "defect_C", 2: "defect_D"}

def draw_yolo_prediction(image, result, color=(0, 255, 0), thickness=5):
    for box in result.boxes:
        x1, y1, x2, y2 = box.xyxy[0]
        x1, y1, x2, y2 = int(x1), int(y1), int(x2), int(y2)
        cv2.rectangle(image, (x1, y1), (x2, y2), color, thickness)
        cv2.putText(image, f"{map_class[int(box.cls)]}", (x1, y1 - 5), cv2.FONT_HERSHEY_SIMPLEX, 3, color, 5)
        print("FOUND DEFECT", map_class[int(box.cls)])
    return image


prediction_frame = draw_yolo_prediction(rgb_image.copy(), results[0], color=(255, 0, 0), thickness=5)

fig, axs = plt.subplots(1, 3, figsize=(15, 5))
axs[0].imshow(rgb_image)
axs[0].set_title("Original Image")
axs[0].axis("off")

axs[1].imshow(draw_frame)
axs[1].set_title("Groundtruth Image")
axs[1].axis("off")

axs[2].imshow(prediction_frame)
axs[2].set_title("Prediction Image")

for filename in image_filenames:
    image_path = os.path.join(TEST_SET_IMAGE, filename)
    # label_path = os.path.join(TEST_SET_LABEL, filename.replace(".jpg", ".txt"))
    rgb_image = cv2.cvtColor(cv2.imread(image_path), cv2.COLOR_BGR2RGB)
    results = trained_model.predict(rgb_image, task='detect', verbose=False)
    results[0].save_txt(f'../data/process_data/[SNL]_data/test_set/prediction/{filename.replace(".jpg", ".txt")}')


import os
import numpy as np
from sklearn.metrics import confusion_matrix, precision_score, recall_score, f1_score
from collections import defaultdict
import matplotlib.pyplot as plt
import seaborn as sns

def yolo_to_xyxy(box, img_w=1, img_h=1):
    cls, x, y, w, h = box
    x1 = (x - w / 2) * img_w
    y1 = (y - h / 2) * img_h
    x2 = (x + w / 2) * img_w
    y2 = (y + h / 2) * img_h
    return int(cls), x1, y1, x2, y2

def box_iou(boxA, boxB):
    xa1, ya1, xa2, ya2 = boxA
    xb1, yb1, xb2, yb2 = boxB

    inter_x1 = max(xa1, xb1)
    inter_y1 = max(ya1, yb1)
    inter_x2 = min(xa2, xb2)
    inter_y2 = min(ya2, yb2)

    inter_area = max(0, inter_x2 - inter_x1) * max(0, inter_y2 - inter_y1)
    areaA = (xa2 - xa1) * (ya2 - ya1)
    areaB = (xb2 - xb1) * (yb2 - yb1)
    union = areaA + areaB - inter_area
    return inter_area / union if union > 0 else 0

def evaluate_yolo_predictions(pred_dir, label_dir, iou_threshold=0.5, class_names=None):
    y_true_all, y_pred_all = [], []

    TP = 0
    FP = 0
    FN = 0

    all_class_ids = set()

    for filename in os.listdir(label_dir):
        label_path = os.path.join(label_dir, filename)
        pred_path = os.path.join(pred_dir, filename)

        if not os.path.exists(pred_path):
            continue

        with open(label_path, 'r') as f:
            gt_boxes = [list(map(float, line.strip().split())) for line in f.readlines()]
        with open(pred_path, 'r') as f:
            pred_boxes = [list(map(float, line.strip().split())) for line in f.readlines()]

        gt_objs = [yolo_to_xyxy(b) for b in gt_boxes]
        pred_objs = [yolo_to_xyxy(b) for b in pred_boxes]

        matched_gt = set()
        matched_pred = set()

        for pi, (p_cls, *p_box) in enumerate(pred_objs):
            all_class_ids.add(int(p_cls))
            best_iou = 0
            best_gi = -1
            for gi, (g_cls, *g_box) in enumerate(gt_objs):
                if gi in matched_gt:
                    continue
                if int(p_cls) != int(g_cls):
                    continue
                iou = box_iou(p_box, g_box)
                if iou > best_iou:
                    best_iou = iou
                    best_gi = gi
            if best_iou >= iou_threshold:
                TP += 1
                matched_gt.add(best_gi)
                matched_pred.add(pi)
                y_true_all.append(p_cls)
                y_pred_all.append(p_cls)
            else:
                FP += 1
                y_true_all.append(-1)  # false detection
                y_pred_all.append(p_cls)

        for gi, (g_cls, *_) in enumerate(gt_objs):
            all_class_ids.add(int(g_cls))
            if gi not in matched_gt:
                FN += 1
                y_true_all.append(g_cls)
                y_pred_all.append(-1)

    # Classification metrics
    labels = sorted(list(all_class_ids))
    precision = precision_score(y_true_all, y_pred_all, labels=labels, average='macro', zero_division=0)
    recall = recall_score(y_true_all, y_pred_all, labels=labels, average='macro', zero_division=0)
    f1 = f1_score(y_true_all, y_pred_all, labels=labels, average='macro', zero_division=0)
    accuracy = TP / (TP + FP + FN) if TP + FP + FN > 0 else 0

    # mAP@50 simplified as average of per-class precision (proxy)
    cm = confusion_matrix(y_true_all, y_pred_all, labels=labels + [-1])

    print(f"Precision: {precision:.3f}")
    print(f"Recall:    {recall:.3f}")
    print(f"F1 Score:  {f1:.3f}")
    print(f"Accuracy (IoU@{iou_threshold}): {accuracy:.3f}")

    if class_names:
        labels_str = [class_names[i] for i in labels] + ['background']
    else:
        labels_str = [f'class_{i}' for i in labels] + ['background']

    plt.figure(figsize=(8, 6))
    sns.heatmap(cm, annot=True, fmt="d", xticklabels=labels_str, yticklabels=labels_str, cmap='Blues')
    plt.title("Confusion Matrix")
    plt.xlabel("Predicted")
    plt.ylabel("True")
    plt.tight_layout()
    plt.show()

    return {
        'precision': precision,
        'recall': recall,
        'f1': f1,
        'accuracy': accuracy,
        'confusion_matrix': cm
    }


metrics = evaluate_yolo_predictions(
    pred_dir="../data/process_data/[SNL]_data/test_set/prediction",
    label_dir="../data/process_data/[SNL]_data/test_set/test_set_label/obj_train_data",
    iou_threshold=0.5,
    class_names=["defect_B", "defect_C", "defect_D"]
)

# PREPARE CLASSIFICATION

# DATA DISTRIBUTION