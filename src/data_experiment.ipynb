# Ad-hoc function to add index in front of the filename

import os
import shutil
from glob import glob
from natsort import natsorted

import os
import re

def natural_sort_key(s):
    return [int(text) if text.isdigit() else text for text in re.split('([0-9]+)', s)]

def add_image_indexing(dir_path):

    files_paths = os.listdir(dir_path)
    # Separate files based on leading special character
    alpha_numeric_files = [f for f in files_paths if not f.startswith('#')]
    special_char_files = [f for f in files_paths if f.startswith('#')]

    # Sort each group
    sorted_alpha_numeric_files = natsorted(alpha_numeric_files)
    sorted_special_char_files = natsorted(special_char_files, reverse=True)

    # Combine lists to match MacOS Finder sorting style
    sorted_filenames = sorted_alpha_numeric_files + sorted_special_char_files

    for index,filename in enumerate(sorted_filenames):
        old_filename = filename
        new_filename = f"{index + 1}_{old_filename}"
        shutil.move(
            os.path.join(dir_path, old_filename),
            os.path.join(dir_path, new_filename)
        )
    print("Move done")

IMAGE_POOL_PATHS = "../train_data/sonic_logging_fold_1"
add_image_indexing(IMAGE_POOL_PATHS)

from tqdm import tqdm
import os
from pdf2image import convert_from_path
from PIL import Image

# Constants
TASK3_POOL = "../raw_data_pool/Task3-Sample"
W1_PERCENTAGE = 5 / 100
W2_PERCENTAGE = 100 / 100
H1_PERCENTAGE = 18.5 / 100
H2_PERCENTAGE = 92 / 100

def run_crop_process(pdf_path: str, output_dir: str, file_idx: int, n_fold: int=3):
    """
    Processes a PDF by converting its pages to images, cropping them, 
    and saving the cropped images to a folder determined by file_idx.

    Args:
        pdf_path (str): Path to the PDF file.
        output_dir (str): Directory to save the cropped images.
        file_idx (int): Index of the file (used to determine the fold number).
    """
    if not os.path.exists(pdf_path):
        raise FileNotFoundError(f"The file '{pdf_path}' does not exist.")
    if not pdf_path.lower().endswith('.pdf'):
        raise ValueError("The provided file is not a PDF.")
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)

    # Extract the base name for the PDF file to use in image filenames
    pdf_name = os.path.basename(pdf_path).rsplit('.', 1)[0]

    # Step 1: Read and convert the PDF to images
    try:
        images = convert_from_path(pdf_path, dpi=300)
    except Exception as e:
        raise RuntimeError(f"Error converting PDF to images: {e}")

    # Step 2: Process each image
    for i, img in enumerate(images):
        # Convert image to width (w) and height (h)
        w, h = img.size

        # Calculate crop dimensions based on percentages
        left = int(w * W1_PERCENTAGE)
        right = int(w * W2_PERCENTAGE)
        top = int(h * H1_PERCENTAGE)
        bottom = int(h * H2_PERCENTAGE)

        # Crop the image
        cropped_img = img.crop((left, top, right, bottom))

        # Determine the folder (fold) based on the file index
        fold_number = file_idx % n_fold
        fold_path = os.path.join(output_dir, f"fold_{fold_number}")
        os.makedirs(fold_path, exist_ok=True)

        # Construct the output file name using the PDF name and page index
        output_file = os.path.join(fold_path, f"{pdf_name}_page_{i + 1}.jpg")
        cropped_img.save(output_file)


DATA_FOLDER = "../data/process_data/[SNL]_data/test_set/PDF_sonic_raw_data"
OUTPUT_DIR = "../data/process_data/[SNL]_data/for_label"

# Main script
if __name__ == "__main__":
    try:

        # List all PDF files in TASK3_POOL
        pdf_files = [f for f in os.listdir(DATA_FOLDER) if f.lower().endswith('.pdf')]

        # Iterate over PDFs with progress tracking using tqdm
        for file_idx, pdf_file in enumerate(tqdm(pdf_files, desc="Processing PDFs")):
            pdf_path = os.path.join(DATA_FOLDER, pdf_file)
            run_crop_process(
                pdf_path,
                OUTPUT_DIR,
                file_idx=file_idx+1,
                n_fold=1,
            )
    except Exception as error:
        print(f"Error: {error}")

from pdf2image import convert_from_path
from typing import List
import os

SAMPLE_PDF = os.listdir(
    TASK3_POOL
)[0]


def read_pdf_file(pdf_path: str) -> List[str]:
    """
    Reads the PDF file and ensures it exists.
    Args:
        pdf_path (str): Path to the PDF file.

    Returns:
        List[str]: Path of the images converted from the PDF.
    """
    if not os.path.exists(pdf_path):
        raise FileNotFoundError(f"The file '{pdf_path}' does not exist.")
    if not pdf_path.lower().endswith('.pdf'):
        raise ValueError("The provided file is not a PDF.")
    
    return convert_pdf_to_images(pdf_path)

def convert_pdf_to_images(pdf_path: str, dpi: int = 300) -> List[str]:
    """
    Converts a PDF file to images.
    Args:
        pdf_path (str): Path to the PDF file.
        dpi (int): Dots per inch for image quality. Defaults to 300.

    Returns:
        List[str]: Paths to the image files generated.
    """
    try:
        images = convert_from_path(pdf_path, dpi=dpi)
        image_paths = []
        for i, img in enumerate(images):
            image_path = f"output_page_{i + 1}.png"
            img.save(image_path, "PNG")
            image_paths.append(image_path)
        return image_paths
    except Exception as e:
        raise RuntimeError(f"Failed to convert PDF to images: {e}")

def get_sample_frame_from_pdf_file(pdf_path: str) -> str:
    """
    Gets a sample frame (image) from the first page of a PDF file.
    Args:
        pdf_path (str): Path to the PDF file.

    Returns:
        str: Path to the image of the first page of the PDF.
    """
    image_paths = read_pdf_file(pdf_path)
    if not image_paths:
        raise ValueError("No pages found in the PDF.")
    return image_paths[0]  # Return the first page's image path

# # Example Usage:
# pdf_path = "example.pdf"
sample_image_path = get_sample_frame_from_pdf_file(os.path.join(
    TASK3_POOL,
    SAMPLE_PDF
))
print(f"Sample frame saved as: {sample_image_path}")


SAMPLE_PDF

# STEP 1 : Prepare train data from image pool and labeled folder
"""
Scratchpad:
    Additional notes
    1. labelled data was in the format of Fold_1/obj_train_data/<filename>.txt in the YOLO format
    2. Image data was in data/pool_data/train_data/sonic_logging_fold_1/*.jpg

    Additional : path for labeled folder ./data/labelled_data/sonic-logging-labelled/Fold_1 , Fold_2, ...
    

    Step to implemented:
    1. Count positive and negative data from the label folder, (Positive = .txt file have the information, Negative = .txt is just a plain file)
    2. Get negative data and labeleed from 30% of instances of potitive data
    3. From the fold_folder it contain Fold_1, Fold_2, ... Fold_10 -> Fold 9 and 10 should be prepare for testing data
    4. Divided 20% of prepare data from Fold_1, Fold_2, ... Fold_8 to be validation set
    5. End goal images should be train, val, test 

    Goal: 
        - To prepare the information as the YOLO trainable format
        - YOLO Hierachy format , images/train, val : labels/train, val : data_train.yaml

"""

import cv2
import os

import cv2
import matplotlib.pyplot as plt

def visualize_yolo_bboxes(image, bboxes, class_map=None):
    """
    Visualizes YOLO bounding boxes on an image.

    Parameters:
    - image (ndarray): Input image as a NumPy array.
    - bboxes (list): List of YOLO bounding boxes in the format:
    [class_id, x_center, y_center, width, height].
    - class_map (dict): Optional mapping of class IDs to class names.
    
    Returns:
    - annotated_image (ndarray): Image with bounding boxes drawn.
    """
    # Create a copy of the image for annotation
    annotated_image = image.copy()

    # Get image dimensions
    height, width = image.shape

    # Iterate through bounding boxes
    for bbox in bboxes:
        class_id, x_center, y_center, bbox_width, bbox_height = map(float, bbox.split())

        # Convert YOLO format (normalized) to absolute coordinates
        x_center_abs = int(x_center * width)
        y_center_abs = int(y_center * height)
        bbox_width_abs = int(bbox_width * width)
        bbox_height_abs = int(bbox_height * height)

        # Calculate top-left and bottom-right coordinates
        x_min = int(x_center_abs - bbox_width_abs / 2)
        y_min = int(y_center_abs - bbox_height_abs / 2)
        x_max = int(x_center_abs + bbox_width_abs / 2)
        y_max = int(y_center_abs + bbox_height_abs / 2)

        # Draw rectangle for the bounding box
        color = (0, 255, 0)  # Green for bounding boxes
        cv2.rectangle(annotated_image, (x_min, y_min), (x_max, y_max), color, 2)

        # Add label text if class_map is provided
        if class_map:
            label = class_map.get(int(class_id), str(class_id))
            cv2.putText(
                annotated_image,
                label,
                (x_min, y_min - 10),
                cv2.FONT_HERSHEY_SIMPLEX,
                0.5,
                color,
                2
            )

    return annotated_image



def preprocess_label(
        image_file_path: str,
        label_file_path: str,
        is_save_new_label: bool = False
):
    """
    Preprocesses the image and its corresponding labels by:
    1. Cropping 15% from the top.
    2. Cropping 20% from the left.
    3. Converting the image to grayscale and applying histogram equalization.
    4. Adjusting label bounding boxes based on cropping and new image dimensions.
    5. Expanding bounding boxes to the right up to the maximum width.

    Parameters:
    - image_file_path (str): Path to the image file.
    - label_file_path (str): Path to the label file.

    Returns:
    - processed_image (ndarray): The preprocessed image.
    - updated_labels (list): Updated label information after processing.
    """
    # Read the image
    image = cv2.imread(image_file_path)
    if image is None:
        raise FileNotFoundError(f"Image not found: {image_file_path}")

    # Read the label file
    if not os.path.exists(label_file_path):
        raise FileNotFoundError(f"Label file not found: {label_file_path}")

    with open(label_file_path, "r") as file:
        labels = file.readlines()

    # Get original dimensions
    height, width, _ = image.shape

    # Define cropping fractions
    crop_top_fraction = 0.15
    crop_left_fraction = 0.20

    # Calculate cropping dimensions
    crop_top = int(crop_top_fraction * height)
    crop_left = int(crop_left_fraction * width)

    # Crop the image
    cropped_image = image[crop_top:, crop_left:, :]

    # Get new dimensions
    new_height, new_width, _ = cropped_image.shape

    # Update labels
    updated_labels = []
    for label in labels:
        class_id, x_center, y_center, bbox_width, bbox_height = map(float, label.split())

        # Convert normalized coordinates to absolute
        x_center_abs = x_center * width
        y_center_abs = y_center * height
        bbox_width_abs = bbox_width * width
        bbox_height_abs = bbox_height * height

        # Calculate absolute bounding box coordinates
        x_min = x_center_abs - bbox_width_abs / 2
        x_max = x_center_abs + bbox_width_abs / 2
        y_min = y_center_abs - bbox_height_abs / 2
        y_max = y_center_abs + bbox_height_abs / 2

        # Adjust bounding box coordinates for the cropped area
        x_min -= crop_left
        x_max -= crop_left
        y_min -= crop_top
        y_max -= crop_top

        # Skip labels if the bounding box falls completely outside the cropped area
        if x_max <= 0 or y_max <= 0 or x_min >= new_width or y_min >= new_height:
            continue

        # Clip bounding box to be within the cropped image bounds
        x_min = max(0, x_min)
        y_min = max(0, y_min)
        x_max = min(new_width, x_max)
        y_max = min(new_height, y_max)

        # Expand bounding box to the right while keeping the left side fixed
        x_max = new_width

        # Recalculate normalized coordinates based on the new dimensions
        x_center_new = (x_min + x_max) / 2 / new_width
        y_center_new = (y_min + y_max) / 2 / new_height
        bbox_width_new = (x_max - x_min) / new_width
        bbox_height_new = (y_max - y_min) / new_height

        updated_labels.append(f"{class_id} {x_center_new:.6f} {y_center_new:.6f} {bbox_width_new:.6f} {bbox_height_new:.6f}\n")

    # Convert the cropped image to grayscale and apply histogram equalization
    gray_image = cv2.cvtColor(cropped_image, cv2.COLOR_BGR2GRAY)
    processed_image = cv2.equalizeHist(gray_image)

    # Updated new label, expand bbox
    final_labels = adjust_bbox_vertical(
        updated_labels,
        processed_image.shape[1],
        processed_image.shape[0],
        move_y_fraction=0.01
    )

    # Save updated label file if specified
    if is_save_new_label:
        with open(label_file_path, "w") as file:
            file.writelines(final_labels)

    return processed_image, final_labels


import matplotlib.pyplot as plt

root_dir = "./data/yolo_prepared/"
image_file = "../data/yolo_prepared/images/train/6751_#P351R_Defect_6.jpg"
label_file = "../data/yolo_prepared/labels/train/6751_#P351R_Defect_6.txt"

image, new_labels = preprocess_label(
    image_file,
    label_file
)

annotated_frame = visualize_yolo_bboxes(image, new_labels)
plt.imshow(annotated_frame, cmap='gray')



import os
import random
from shutil import copy2
from tqdm import tqdm
from natsort import natsorted

def prepare_train_data(image_pool_folder, labelled_folder, output_folder):
    """
    Prepare train, validation, and test data in YOLO format from labeled and image pool folders.

    Args:
        image_pool_folder (str): Path to the folder containing all images.
        labelled_folder (str): Path to the folder containing labeled data (YOLO format).
        output_folder (str): Path to the output folder for prepared YOLO data.

    Returns:
        None
    """
    random.seed(42)  # For reproducibility

    # Create output directories
    for subfolder in ['images/train', 'images/val', 'images/test', 'labels/train', 'labels/val', 'labels/test']:
        os.makedirs(os.path.join(output_folder, subfolder), exist_ok=True)

    # Step 1: Count positive and negative data (only from Fold_1 to Fold_8)
    positive_files = []
    negative_files = []

    for root, _, files in os.walk(labelled_folder):
        fold_name = os.path.basename(root)
        if fold_name.startswith("Fold_") and int(fold_name.split("_")[1]) <= 9:
            # Path to the obj_train_data folder
            obj_train_data_path = os.path.join(root, 'obj_train_data')
            if os.path.exists(obj_train_data_path):
                for file in os.listdir(obj_train_data_path):
                    if file.endswith('.txt'):
                        label_path = os.path.join(obj_train_data_path, file)
                        if os.path.getsize(label_path) > 0:
                            positive_files.append(label_path)  # Non-empty .txt files are positive
                        else:
                            negative_files.append(label_path)  # Empty .txt files are negative

    # Step 2: Get 30% of negative data based on positive instances
    num_positive = len(positive_files)
    num_negative_to_include = int(0.3 * num_positive)
    selected_negative_files = random.sample(negative_files, min(num_negative_to_include, len(negative_files)))
    print("Check selected negavtive length", len(selected_negative_files))

    # Combine positive and selected negative files
    trainable_files = positive_files + selected_negative_files
    random.shuffle(trainable_files)

    print("Length of selected trainable files", len(trainable_files))

    # Step 3: Split data into train, val, test based on folds
    folds = natsorted([f for f in os.listdir(labelled_folder) if os.path.isdir(os.path.join(labelled_folder, f))])
    test_folds = folds[-1:]  # Last two folds for testing
    train_val_folds = folds[:-1]  # Remaining folds for training and validation

    train_files, val_files, test_files = [], [], []

    # Prepare test files first
    for fold in test_folds:
        obj_train_data_path = os.path.join(labelled_folder, fold, 'obj_train_data')
        if os.path.exists(obj_train_data_path):
            for file in os.listdir(obj_train_data_path):
                if file.endswith('.txt'):
                    test_files.append(os.path.join(obj_train_data_path, file))

    # Split trainable_files into train and val
    for label_file in trainable_files:
        fold_name = label_file.split('/')[4]  # Assuming a fixed directory structure

        if fold_name in train_val_folds:
            if random.random() < 0.2:
                val_files.append(label_file)
            else:
                train_files.append(label_file)
    # Step 5: Copy data into YOLO hierarchy
    def copy_yolo_files(label_files, image_pool_folder, image_dest, label_dest):

        skipped_files = 0  # Track skipped files
        for label_file in tqdm(label_files, desc=f"Copying files to {image_dest}"):
            base_name = os.path.splitext(os.path.basename(label_file))[0]
            image_file = os.path.join(image_pool_folder, base_name + '.jpg')

            if os.path.exists(image_file):
                copy2(image_file, os.path.join(image_dest, base_name + '.jpg'))
                copy2(label_file, os.path.join(label_dest, base_name + '.txt'))
            else:
                skipped_files += 1
        
        # Log the number of skipped files
        print(f"Skipped {skipped_files} files due to missing images.")
    def copy_yolo_files_with_preprocessing(label_files, image_pool_folder, image_dest, label_dest):
        skipped_files = 0  # Track skipped files

        for label_file in tqdm(label_files, desc=f"Processing and saving files to {image_dest}"):
            base_name = os.path.splitext(os.path.basename(label_file))[0]
            image_file = os.path.join(image_pool_folder, base_name + '.jpg')

            # Check if the source image exists
            if not os.path.exists(image_file):
                skipped_files += 1
                continue

            # Preprocess the image and annotations
            processed_image, processed_annotations = preprocess_label(
                image_file_path=image_file,
                label_file_path=label_file,
            )

            # Save the processed image
            processed_image_path = os.path.join(image_dest, base_name + '.jpg')
            cv2.imwrite(processed_image_path, processed_image)

            # Save the processed annotations
            processed_label_path = os.path.join(label_dest, base_name + '.txt')
            with open(processed_label_path, 'w') as f:
                f.writelines(processed_annotations)

        # Log the number of skipped files
        print(f"Skipped {skipped_files} files due to missing images.")


    # copy_yolo_files(train_files, image_pool_folder, os.path.join(output_folder, 'images/train'), os.path.join(output_folder, 'labels/train'))
    # copy_yolo_files(val_files, image_pool_folder, os.path.join(output_folder, 'images/val'), os.path.join(output_folder, 'labels/val'))
    # copy_yolo_files(test_files, image_pool_folder, os.path.join(output_folder, 'images/test'), os.path.join(output_folder, 'labels/test'))

    copy_yolo_files_with_preprocessing(train_files, image_pool_folder, os.path.join(output_folder, 'images/train'), os.path.join(output_folder, 'labels/train'))
    copy_yolo_files_with_preprocessing(val_files, image_pool_folder, os.path.join(output_folder, 'images/val'), os.path.join(output_folder, 'labels/val'))
    copy_yolo_files_with_preprocessing(test_files, image_pool_folder, os.path.join(output_folder, 'images/test'), os.path.join(output_folder, 'labels/test'))


    # Prepare data_train.yaml
    yaml_content = f"""train: {os.path.abspath(os.path.join(output_folder, 'images/train'))}
val: {os.path.abspath(os.path.join(output_folder, 'images/val'))}
test: {os.path.abspath(os.path.join(output_folder, 'images/test'))}

nc: 1  # Number of classes (update as needed)
names: ['class_name']  # Replace with actual class names
"""

    with open(os.path.join(output_folder, 'data_train.yaml'), 'w') as yaml_file:
        yaml_file.write(yaml_content)

    print("YOLO data preparation complete.")


# Example usage:
prepare_train_data(
    image_pool_folder="../data/pool_data/train_data/sonic_logging_fold_1",
    labelled_folder="../data/labelled_data/sonic-logging-labelled",
    output_folder="../data/yolo_prepared"
)

import os
import random
import cv2
from tqdm import tqdm
from natsort import natsorted

def mutate_new_label(label_lines):
    # Mutate labels
    mutated_lines = []
    mutated_count = 0
    for line in label_lines:
        elements = line.split()
        if elements:
            # Change the class label to a single digit integer if needed
            class_label = elements[0]
            if '.' in class_label:  # Check for invalid class format
                elements[0] = str(int(float(class_label)))  # Convert to one digit
                mutated_count += 1

        mutated_lines.append(' '.join(elements) + '\n')

    return mutated_lines

def adjust_bbox_vertical(
        updated_labels, new_width, new_height, move_y_fraction=0.1):
    """
    Adjusts YOLO bounding boxes by expanding in the vertical direction.

    Parameters:
    - updated_labels (list): List of YOLO labels in format [class_id, x_center, y_center, width, height].
    - new_width (int): Width of the processed image.
    - new_height (int): Height of the processed image.
    - move_y_fraction (float): Fraction of the height to adjust `y1` (up) and `y2` (down).

    Returns:
    - adjusted_labels (list): Updated labels after vertical adjustment.
    """
    adjusted_labels = []

    for label in updated_labels:
        class_id, x_center, y_center, bbox_width, bbox_height = map(float, label.split())

        # Convert normalized to absolute
        x_center_abs = x_center * new_width
        y_center_abs = y_center * new_height
        bbox_width_abs = bbox_width * new_width
        bbox_height_abs = bbox_height * new_height

        # Calculate current absolute bbox coordinates
        x_min = x_center_abs - bbox_width_abs / 2
        x_max = x_center_abs + bbox_width_abs / 2
        y_min = y_center_abs - bbox_height_abs / 2
        y_max = y_center_abs + bbox_height_abs / 2

        # Adjust y1 (up) and y2 (down)
        y_min -= move_y_fraction * new_height
        y_max += move_y_fraction * new_height

        # Clip y1 and y2 to image bounds
        y_min = max(0, y_min)
        y_max = min(new_height, y_max)

        # Recalculate normalized coordinates
        x_center_new = (x_min + x_max) / 2 / new_width
        y_center_new = (y_min + y_max) / 2 / new_height
        bbox_width_new = (x_max - x_min) / new_width
        bbox_height_new = (y_max - y_min) / new_height

        adjusted_labels.append(f"{class_id} {x_center_new:.6f} {y_center_new:.6f} {bbox_width_new:.6f} {bbox_height_new:.6f}\n")

    return adjusted_labels


def preprocess_label(image_file_path: str, label_file_path: str, crop_ratio=0.15):
    """
    Preprocess information

    """
    # APPLY CLAHE
    def processed_auto_contrast(image):
        lab = cv2.cvtColor(image, cv2.COLOR_RGB2LAB)
        l_channel, a_channel, b_channel = cv2.split(lab)
        clahe = cv2.createCLAHE(clipLimit=2.0, tileGridSize=(8, 8))

        cl = clahe.apply(l_channel)
        lab_clahe = cv2.merge((cl, a_channel, b_channel))

        result_image = cv2.cvtColor(lab_clahe, cv2.COLOR_LAB2RGB)

        return result_image
    
    def crop_top_image(image, labels, crop_ratio):
        height, width, _ = image.shape

        crop_top_fraction = crop_ratio
        crop_top = int(crop_top_fraction * height)

        cropped_image = image[crop_top: , :, :]
        # Get new dimensions
        new_height, new_width, _ = cropped_image.shape
        updated_labels = []
        for label in labels:
            class_id, x_center, y_center, bbox_width, bbox_height = map(float, label.split())
            
            # Convert normalized coordinates to absolute
            x_center_abs = x_center * width
            y_center_abs = y_center * height
            bbox_width_abs = bbox_width * width
            bbox_height_abs = bbox_height * height

            # Calculate absolute bounding box coordinates
            x_min = x_center_abs - bbox_width_abs / 2
            x_max = x_center_abs + bbox_width_abs / 2
            y_min = y_center_abs - bbox_height_abs / 2
            y_max = y_center_abs + bbox_height_abs / 2

            # Adjust bounding box coordinates for the cropped area
            y_min -= crop_top
            y_max -= crop_top

            # Skip labels if the bounding box falls completely outside the cropped area
            if x_max <= 0 or y_max <= 0 or x_min >= new_width or y_min >= new_height:
                continue

            # Clip bounding box to be within the cropped image bounds
            x_min = max(0, x_min)
            y_min = max(0, y_min)
            x_max = min(new_width, x_max)
            y_max = min(new_height, y_max)

            # Expand bounding box to the right while keeping the left side fixed
            x_max = new_width

            # Recalculate normalized coordinates based on the new dimensions
            x_center_new = (x_min + x_max) / 2 / new_width
            y_center_new = (y_min + y_max) / 2 / new_height
            bbox_width_new = (x_max - x_min) / new_width
            bbox_height_new = (y_max - y_min) / new_height

            updated_labels.append(f"{class_id} {x_center_new:.6f} {y_center_new:.6f} {bbox_width_new:.6f} {bbox_height_new:.6f}\n")

        return cropped_image, updated_labels

    # Read the image
    image = cv2.imread(image_file_path)
    if image is None:
        # print(f"Image not found: {image_file_path}")
        return None, None

    # Read the label file
    if not os.path.exists(label_file_path):
        # print(f"Label file not found: {label_file_path}")
        return None, None

    with open(label_file_path, "r") as file:
        labels = file.readlines()
    
    processed_image, updated_labels=crop_top_image(image, labels,crop_ratio) # Crop image and also updated label follow the cropping
    processed_image=processed_auto_contrast(processed_image)
    update_labels=adjust_bbox_vertical(updated_labels, 
                                        processed_image.shape[1], processed_image.shape[0],
                                        move_y_fraction=0.05)
    

    return processed_image, update_labels


def prepare_preprocess_data(image_pool_folder, labelled_folder, output_folder):
    random.seed(42)

    # Create output directories
    for subfolder in ['images/train', 'images/val', 'images/test', 'labels/train', 'labels/val', 'labels/test']:
        os.makedirs(os.path.join(output_folder, subfolder), exist_ok=True)

    # Step 1: Count positive and negative data (only from Fold_1 to Fold_9)
    positive_files = []
    negative_files = []
    sample_data_instances = 0

    for root, _, files in os.walk(labelled_folder):
        fold_name = os.path.basename(root)
        if fold_name.startswith("Fold_") and int(fold_name.split("_")[1]) <= 9:
            obj_train_data_path = os.path.join(root, 'obj_train_data')
            sample_data_instances += len(os.listdir(obj_train_data_path))
            if os.path.exists(obj_train_data_path):
                for file in os.listdir(obj_train_data_path):
                    if file.endswith('.txt'):
                        label_path = os.path.join(obj_train_data_path, file)
                        if os.path.getsize(label_path) > 0:
                            positive_files.append(label_path)  # Non-empty .txt files are positive
                        else:
                            negative_files.append(label_path)  # Empty .txt files are negative


    print("Total labels file to process", sample_data_instances)
    print("Check Positive from fold 1-9", len(positive_files))
    print("Check Negative from fold 1-9", len(negative_files))
    
    folds = natsorted([f for f in os.listdir(labelled_folder) if os.path.isdir(os.path.join(labelled_folder, f))])
    test_folds = folds[-1:]  # Last fold for testing
    # train_val_folds = folds[:-1]  # Remaining folds for training and validation

    def copy_yolo_files_with_preprocessing(label_files, image_pool_folder, image_dest, label_dest):
        skipped_files = 0
        for label_file in tqdm(label_files, desc=f"Processing files for {image_dest}"):
            base_name = os.path.splitext(os.path.basename(label_file))[0]
            image_file = os.path.join(image_pool_folder, base_name + '.jpg')

            if not os.path.exists(image_file):
                skipped_files += 1
                continue

            processed_image, processed_annotations = preprocess_label(
                image_file_path=image_file,
                label_file_path=label_file,
                crop_ratio=0.10
            )

            # Save the processed image
            cv2.imwrite(os.path.join(image_dest, base_name + '.jpg'), processed_image)

            # Save the processed annotations
            with open(os.path.join(label_dest, base_name + '.txt'), 'w') as f:
                f.writelines(processed_annotations)

        print(f"Skipped {skipped_files} files due to missing images.")

    skipped_files = 0
    all_image_in_pool = os.listdir(image_pool_folder) # With index prefix 1_filname.jpg
    # processed_positive_label_file_path = []
    
    def find_matching_file(base_name, file_list):
        """ DO EXACTLY MATCH """
        for file_name in file_list:
            # Split the filename into index and basename
            parts = file_name.split("_", 1)
            if len(parts) > 1 and parts[1].split(".")[0] == base_name:
                return file_name
        return None
    
    for positive_label_file_path in positive_files:
        base_name = os.path.splitext(os.path.basename(positive_label_file_path))[0]
        image_file = os.path.join(image_pool_folder, base_name + '.jpg')
        fold_number = int(positive_label_file_path.split("/")[4].split("_")[-1])

        # remove index check by includes, MUTATES base_name
        # Fix implementation only fold1-fold3
        if fold_number < 4: 
            image_filename_to_process = find_matching_file(base_name, all_image_in_pool)
            # MUTATES image_file path
            image_file = os.path.join(image_pool_folder, image_filename_to_process)
            # print("Check mutates image file", image_file)

        processed_image, processed_annotations = preprocess_label(
            image_file_path=image_file,
            label_file_path=positive_label_file_path,
            crop_ratio=0.10
        )
        if processed_image is None or processed_annotations is None:
            skipped_files += 1
            continue
        mutated_processed_annotations = mutate_new_label(processed_annotations)

        if mutated_processed_annotations:
            # processed_positive_label_file_path.append(base_name)
            processed_image_pool_folder = "../data/pool_data/tmp_processed_data/images"
            processed_label_pool_folder = "../data/pool_data/tmp_processed_data/labels"

            os.makedirs(processed_image_pool_folder, exist_ok=True)
            os.makedirs(processed_label_pool_folder, exist_ok=True)

            cv2.imwrite(os.path.join(processed_image_pool_folder, base_name + '.jpg'), processed_image)
            with open(os.path.join(processed_label_pool_folder, base_name + '.txt'), 'w') as f:
                f.writelines(mutated_processed_annotations)

    # Recalculate negative data and sample 20% of negatives
    tmp_processed_image = os.listdir(processed_label_pool_folder)
    processed_positive_label_file_path = [os.path.join(processed_label_pool_folder, f_name) for f_name in tmp_processed_image]
    print("Total file go on preprocessed", len(positive_files))
    print("Positive filed after preprocessed", len(processed_positive_label_file_path))
    
    positive_sampling_instances = len(processed_positive_label_file_path)
    num_negative_to_include = int(0.2 * positive_sampling_instances)
    selected_negative_files = random.sample(negative_files, min(num_negative_to_include, len(negative_files)))

    # Split negatives into train and val
    train_negative_files = selected_negative_files[:len(selected_negative_files) // 2]
    val_negative_files = selected_negative_files[len(selected_negative_files) // 2:]

    # Create train, val, and test file lists
    train_files = processed_positive_label_file_path[:int(0.8 * len(processed_positive_label_file_path))] + train_negative_files
    val_files = processed_positive_label_file_path[int(0.8 * len(processed_positive_label_file_path)):] + val_negative_files

    test_files = []
    for fold in test_folds:
        obj_train_data_path = os.path.join(labelled_folder, fold, 'obj_train_data')
        if os.path.exists(obj_train_data_path):
            for file in os.listdir(obj_train_data_path):
                if file.endswith('.txt'):
                    test_files.append(os.path.join(obj_train_data_path, file))

    
    print("Actual number for training", len(train_files))
    print("Actual number for validating", len(val_files))
    print("Actual number for testing", len(test_files))

    # Copy files to respective folders
    copy_yolo_files_with_preprocessing(test_files, image_pool_folder, os.path.join(output_folder, 'images/test'), os.path.join(output_folder, 'labels/test'))

# Example usage:
prepare_preprocess_data(
    image_pool_folder="../data/pool_data/train_data/sonic_logging_fold_1",
    labelled_folder="../data/labelled_data/sonic-logging-labelled",
    output_folder="../data/yolo_prepared"
)

import os
import shutil

def prepared_yolo_file_format(tmp_processed_folder: str, dest_processed_folder: str):
    """
    Prepares files for YOLO training format by splitting images and labels into train and validation folders.

    Parameters:
    - tmp_processed_folder (str): Path to the temporary folder containing processed images and labels.
    - dest_processed_folder (str): Path to the destination folder for organized YOLO data.
    """
    # Ensure destination folders exist
    train_image_folder = os.path.join(dest_processed_folder, 'images/train')
    val_image_folder = os.path.join(dest_processed_folder, 'images/val')
    train_label_folder = os.path.join(dest_processed_folder, 'labels/train')
    val_label_folder = os.path.join(dest_processed_folder, 'labels/val')
    
    for folder in [train_image_folder, val_image_folder, train_label_folder, val_label_folder]:
        os.makedirs(folder, exist_ok=True)

    # Get all files and split into train and validation sets
    all_files = os.listdir(tmp_processed_folder)
    train_files = all_files[:int(0.8 * len(all_files))]  # 80% for training
    val_files = all_files[int(0.8 * len(all_files)):]    # 20% for validation

    not_found_count = 0

    # Helper function to move files
    def move_files(files, dest_image_folder, dest_label_folder):
        nonlocal not_found_count
        for image_file_name in files:
            # Derive paths
            base_name = os.path.splitext(image_file_name)[0]
            image_file_path = os.path.join(tmp_processed_folder, image_file_name)
            label_file_path = os.path.join(tmp_processed_folder.replace("images", "labels"), base_name + '.txt')

            # Check if label file exists
            if not os.path.exists(label_file_path):
                print(f"Label file not found: {label_file_path}")
                not_found_count += 1
                continue

            # Move image and label files
            shutil.move(image_file_path, os.path.join(dest_image_folder, image_file_name))
            shutil.move(label_file_path, os.path.join(dest_label_folder, base_name + '.txt'))

    # Process train files
    move_files(train_files, train_image_folder, train_label_folder)

    # Process validation files
    move_files(val_files, val_image_folder, val_label_folder)

    print(f"TOTAL LABEL FILES NOT FOUND: {not_found_count}")


prepared_yolo_file_format(
    tmp_processed_folder="../data/pool_data/tmp_processed_data/images",
    dest_processed_folder= "../data/yolo_prepared")



from utils.helper import check_file_distribution, plot_class_distribution, count_and_plot_yolo_train_data,query_class_from_pool


check_file_distribution("../data/yolo_prepared_backup_raw")


from utils.helper import plot_class_distribution

plot_class_distribution("../data/yolo_prepared")

count_and_plot_yolo_train_data("../data/yolo_prepared/labels/train")

query_class_from_pool(
    "../data/yolo_prepared_backup_raw",
    "train",
    0
)

query_class_from_pool(
    "../data/yolo_prepared_backup_raw",
    "train",
    1
)

query_class_from_pool(
    "../data/yolo_prepared_backup_raw",
    "train",
    2
)

# EXPERIMENT PLOT POSITIVE AND NEGATIVE
from utils.helper import check_raw_label_from_fold
check_raw_label_from_fold("../data/labelled_data/sonic-logging-labelled")

from utils.helper import plot_raw_class_distribution

plot_raw_class_distribution("../data/labelled_data/sonic-logging-labelled")


# RECHECK RAW DATA POSITIVE INSTANCES 
def check_positive_raw_data_instances():
    """
    """
    