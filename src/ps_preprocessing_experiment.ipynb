import cv2
import os
import matplotlib.pyplot as plt
import numpy as np

WIDTH_TO_ADJUST = 200

# Define the image file path
image_file = "../data/process_data/for_label/fold_1_03052025/full/3STOREY P sutin.1_page_1.jpg"

# Load the image using OpenCV
image_ = cv2.imread(image_file)
image_rgb = cv2.cvtColor(image_, cv2.COLOR_BGR2RGB)
gray = cv2.cvtColor(image_rgb, cv2.COLOR_RGB2GRAY)
gray = cv2.bitwise_not(gray)
bw = cv2.adaptiveThreshold(gray, 255, cv2.ADAPTIVE_THRESH_MEAN_C, \
                            cv2.THRESH_BINARY, 15, -2)

horizontal = np.copy(bw)
vertical = np.copy(bw)
morph_kernel = np.ones((3, 3),np.uint8)

# Specify size on horizontal axis
cols = horizontal.shape[1]
horizontal_size = cols // WIDTH_TO_ADJUST
# Create structure element for extracting horizontal lines through morphology operations
horizontalStructure = cv2.getStructuringElement(cv2.MORPH_RECT, (horizontal_size, 1))
# Apply morphology operations
horizontal = cv2.erode(horizontal, horizontalStructure)
horizontal = cv2.dilate(horizontal, horizontalStructure)
# Show extracted horizontal lines

# Specify size on vertical axis
rows = vertical.shape[0]
verticalsize = rows // 5
# Create structure element for extracting vertical lines through morphology operations
verticalStructure = cv2.getStructuringElement(cv2.MORPH_RECT, (1, verticalsize))
# Apply morphology operations
vertical = cv2.erode(vertical, verticalStructure)
vertical = cv2.dilate(vertical, verticalStructure)
# Show extracted vertical lines

# # We got mask to remove from the assemble of vertical and horizontal
filter_mask = cv2.bitwise_or(horizontal, vertical)
inverse_mask = cv2.bitwise_not(filter_mask)
process_image = cv2.bitwise_and(bw, bw, mask=inverse_mask)
process_opening = cv2.morphologyEx(process_image, cv2.MORPH_OPEN, morph_kernel)

connectivity_process_filter_image = cv2.medianBlur(process_opening, 3)
process_close = cv2.morphologyEx(connectivity_process_filter_image, cv2.MORPH_CLOSE, morph_kernel)

# Median filter

# # Dilate to glue the jumping 
# process_opening_dilate = cv2.dilate(process_opening, morph_kernel, iterations=1)
# Ensure mask is binary (0 or 255)
# _, mask = cv2.threshold(process_opening_dilate, 127, 255, cv2.THRESH_BINARY)
# Convert back to original image base on the process_opening_dilate foreground
# processing_image = cv2.bitwise_and(image_rgb, image_rgb, mask=mask)


# Combine horizontal and vertical grid line masks
# filter_mask = cv2.bitwise_or(horizontal, vertical)
# _, inpaint_mask = cv2.threshold(filter_mask, 127, 255, cv2.THRESH_BINARY)
# inpainted_image = cv2.inpaint(gray, filter_mask, inpaintRadius=1, flags=cv2.INPAINT_NS)
# _, bw_inpainted = cv2.threshold(inpainted_image, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)
# process_opening = cv2.morphologyEx(inpainted_image, cv2.MORPH_OPEN, morph_kernel)
# process_opening_dilate = cv2.dilate(process_opening, morph_kernel, iterations=1)


# Check if image was loaded successfully
if image_ is None:
    print(f"Error: Could not load image from {image_file}")
else:
    plt.imshow(image_rgb)
    # Plot side-by-side comparison of horizontal and vertical line images
    plt.figure(figsize=(30, 24))

    plt.subplot(2, 2, 1)
    plt.imshow(horizontal, cmap='gray')
    plt.title("Extracted Horizontal Lines")
    plt.axis('off')

    plt.subplot(2, 2, 2)
    plt.imshow(vertical, cmap='gray')
    plt.title("Extracted Vertical Lines")
    plt.axis('off')

    plt.subplot(2, 2, 3)
    plt.imshow(filter_mask, cmap='gray')
    plt.title("Grid line mask")
    plt.axis('off')

    plt.subplot(2, 2, 4)
    plt.imshow(process_close)
    plt.title("Extracted Vertical Lines")
    plt.axis('off')

    plt.tight_layout()
    plt.show()


# Preprocessing and assemble result with a labelling
ANNOTATIONS_PATH = "../data/process_data/labelled/fold_1_03052025/full/annotations.xml"
IMAGE_POOL = "../data/process_data/for_label/fold_1_03052025/full"
INITIAL_POOL = "../data/process_data/for_label/init_pool"

from concave_hull import concave_hull

def remove_gridlines(rgb_image: np.ndarray, width_to_adjust=200, height_to_adjust=5):
    gray = cv2.cvtColor(rgb_image, cv2.COLOR_RGB2GRAY)
    gray = cv2.bitwise_not(gray)
    bw = cv2.adaptiveThreshold(gray, 255, cv2.ADAPTIVE_THRESH_MEAN_C, \
                                cv2.THRESH_BINARY, 15, -2)

    horizontal = np.copy(bw)
    vertical = np.copy(bw)
    morph_kernel = np.ones((3, 3),np.uint8)

    # Specify size on horizontal axis
    cols = horizontal.shape[1]
    horizontal_size = cols // width_to_adjust
    horizontalStructure = cv2.getStructuringElement(cv2.MORPH_RECT, (horizontal_size, 1))
    horizontal = cv2.erode(horizontal, horizontalStructure)
    horizontal = cv2.dilate(horizontal, horizontalStructure)

    # Specify size on vertical axis
    rows = vertical.shape[0]
    verticalsize = rows // height_to_adjust
    verticalStructure = cv2.getStructuringElement(cv2.MORPH_RECT, (1, verticalsize))
    vertical = cv2.erode(vertical, verticalStructure)
    vertical = cv2.dilate(vertical, verticalStructure)

    filter_mask = cv2.bitwise_or(horizontal, vertical)
    inverse_mask = cv2.bitwise_not(filter_mask)
    process_image = cv2.bitwise_and(bw, bw, mask=inverse_mask)
    process_opening = cv2.morphologyEx(process_image, cv2.MORPH_OPEN, morph_kernel)

    connectivity_process_filter_image = cv2.medianBlur(process_opening, 3)
    process_close = cv2.morphologyEx(connectivity_process_filter_image, cv2.MORPH_CLOSE, morph_kernel)

    return np.array(process_close).astype(np.uint8)

def threshold_image(process_image):
    """Threshold the image to highlight peaks and return the thresholded image."""
    three_channel_process_image = np.stack([process_image] * 3, axis=-1)
    gray_img = cv2.cvtColor(three_channel_process_image, cv2.COLOR_RGB2GRAY)
    _, thresholded_img = cv2.threshold(gray_img, 200, 255, cv2.THRESH_BINARY)
    return thresholded_img, three_channel_process_image

def get_all_peaks(thresholded_img):
    """Get all peak points from the thresholded image."""
    peaks = np.column_stack(np.where(thresholded_img == 255))  # Get all peaks (non-zero pixels)
    peaks = peaks[:, [1, 0]]
    return peaks

def calculate_middle_samples(thresholded_img, peaks, offset_x=10):
    """Calculate the middle sample (peak) for each row (trace) and generate new peaks."""
    middle_samples = []
    for row in range(thresholded_img.shape[0]):  # For each row (trace)
        row_peaks = peaks[peaks[:, 1] == row]  # Get all peaks for the current row (filter by y-value)
        if row_peaks.shape[0] > 0:
            peak_x = row_peaks[:, 0].mean()  # Calculate the middle sample (mean of x-coordinates)
            
            # Generate two new peaks based on the middle point
            new_peaks = [
                (peak_x - offset_x, row),  # Peak at x - offset_x
                (peak_x + offset_x, row)   # Peak at x + offset_x
            ]
            middle_samples.extend(new_peaks)  # Add the new peaks to the list

    return np.array(middle_samples)

def filter_peaks_by_distance(peaks, distance_threshold=50):
    """Filter peaks based on a distance threshold to exclude outliers or points too far apart."""
    filtered_peaks = [peaks[0]]  # Always keep the first peak
    for i in range(1, len(peaks)):
        prev_peak = filtered_peaks[-1]
        curr_peak = peaks[i]
        # Calculate the Euclidean distance between the current peak and the previous one
        distance = np.linalg.norm(curr_peak - prev_peak)
        
        if distance < distance_threshold:  # Keep the sample if it's within the threshold distance
            filtered_peaks.append(curr_peak)

    return np.array(filtered_peaks)

def calculate_concave_hull(peaks, three_channel_process_image, concavity_alpha=4.0):
    """Calculate the concave hull using the ConcaveHull library and return the highlighted image."""
    # Apply ConcaveHull to the full set of points (peaks)
    concave_hull_points = concave_hull(peaks, concavity=concavity_alpha)
    
    # Create a copy of the image to apply concave hull
    highlighted_img_concave = three_channel_process_image.copy()
    
    # Draw the concave hull on the image (convert to integer coordinates)
    if len(concave_hull_points) > 0:
        concave_hull_coords = np.array(concave_hull_points, dtype=np.int32)
        cv2.fillPoly(highlighted_img_concave, [concave_hull_coords], (0, 255, 0))  # Green concave hull
    
    # Apply transparency to the concave hull overlay
    alpha = 0.3  # Lower value for more transparency
    overlay_concave = highlighted_img_concave.copy()
    cv2.addWeighted(overlay_concave, alpha, three_channel_process_image, 1 - alpha, 0, highlighted_img_concave)
    
    return highlighted_img_concave

def filter_peaks_by_x_coordinate(peaks, width, threshold_ratio=0.1):
    """Remove peaks located before a specified percentage of the image width."""
    threshold_x = int(threshold_ratio * width)  # Calculate the threshold based on the image width
    filtered_peaks = peaks[peaks[:, 0] >= threshold_x]  # Keep only peaks with x >= threshold_x
    return filtered_peaks

"""
Read image from full and initial with the same file
1. Remove grid line
2. Read labelling
3. Assemble plot
"""
import xml.etree.ElementTree as ET
import pandas as pd
from typing import Tuple, Optional

def parse_annotation_xml(xml_path):
    tree = ET.parse(xml_path)
    root = tree.getroot()

    rows = []

    for image in root.findall("image"):
        image_id = image.attrib.get("id")
        image_name = image.attrib.get("name")
        image_width = image.attrib.get("width")
        image_height = image.attrib.get("height")

        for poly in image.findall("polyline"):
            row = {
                "image/id": image_id,
                "image/name": image_name,
                "image/width": image_width,
                "image/height": image_height,
                "image/polyline/@label": poly.attrib.get("label"),
                "image/polyline/@points": poly.attrib.get("points"),
            }
            rows.append(row)

    return rows

def find_intersection(line1: Tuple[Tuple[float, float], Tuple[float, float]],
                      line2: Tuple[Tuple[float, float], Tuple[float, float]]) -> Optional[Tuple[float, float]]:
    """
    Find the intersection point between two lines (each defined by 2 points).
    Returns None if lines are parallel or coincident.
    """
    (x1, y1), (x2, y2) = line1
    (x3, y3), (x4, y4) = line2

    # Compute the denominator
    denominator = (x1 - x2) * (y3 - y4) - (y1 - y2) * (x3 - x4)

    if denominator == 0:
        return None  # Lines are parallel or coincident

    # Compute the intersection point
    px = ((x1 * y2 - y1 * x2) * (x3 - x4) - 
          (x1 - x2) * (x3 * y4 - y3 * x4)) / denominator
    py = ((x1 * y2 - y1 * x2) * (y3 - y4) - 
          (y1 - y2) * (x3 * y4 - y3 * x4)) / denominator

    return (int(px), int(py))

def random_sampling(df: pd.DataFrame, row: int, col: int,
                    is_middle_point: bool = False, distance_threshold=200,
                    filter_x_ratio=0.05, offset_x=50):
    """
    Randomly sample images and draw two polylines from 'all_polylines'.
    Args:
        df: DataFrame with 'image/name' and 'all_polylines'.
        row: Number of rows in the plot grid.
        col: Number of columns in the plot grid.
    """
    sample_df = df.sample(n=row * col)

    fig, axs = plt.subplots(row, col, figsize=(5 * col, 5 * row))
    axs = axs.flatten()

    for ax, (_, item) in zip(axs, sample_df.iterrows()):
        img_path = os.path.join(IMAGE_POOL, item['image/name'])

        if not os.path.exists(img_path):
            ax.set_title("Image not found")
            ax.axis('off')
            continue

        img = cv2.imread(img_path)
        img = cv2.cvtColor(img, cv2.COLOR_BGR2RGB)
        removed_gridline_image_mask = remove_gridlines(img)
        removed_gridline_three_channel_mask = np.stack([removed_gridline_image_mask]*3, axis=-1)

        # Read init and plot to remove_gridline_three_chanel_mask
        mutate_image_filename = item['image/name'].replace("_p", " (Initial)_p")
        init_img_path = os.path.join(INITIAL_POOL, mutate_image_filename)

        if not os.path.exists(init_img_path):
            print("Image not found from here, INIT POOL", init_img_path)
            ax.set_title("Image not found")
            ax.axis('off')
            continue
        init_img = cv2.imread(init_img_path)
        init_img = cv2.cvtColor(init_img, cv2.COLOR_BGR2RGB)
        remove_gridline_init_image_mask = remove_gridlines(init_img)
        threshold_img, _ = threshold_image(remove_gridline_init_image_mask)
        peaks = get_all_peaks(threshold_img)
        # MUTATE peaks
        peaks = filter_peaks_by_x_coordinate(peaks, img.shape[1], threshold_ratio=filter_x_ratio)
        if is_middle_point:
            middle_samples = calculate_middle_samples(threshold_img, peaks, offset_x=offset_x)
            peaks = middle_samples  # Use the middle samples as the new set of peaks

        filtered_peaks = filter_peaks_by_distance(peaks, distance_threshold)
        highlighted_img_concave = calculate_concave_hull(filtered_peaks, removed_gridline_three_channel_mask)


        # Ensure the mask has the correct type and shape
        assert highlighted_img_concave.dtype == np.uint8, "Mask must be of type uint8."
        assert highlighted_img_concave.shape[2] == 3, "Mask should be a 3-channel image."

        polylines = item['all_polylines']
        for line, _ in zip(polylines[:2], [(255, 0, 0), (0, 255, 0)]):  # l1, l2 with colors
            for i in range(len(line) - 1):
                pt1 = tuple(map(int, line[i]))
                pt2 = tuple(map(int, line[i + 1]))
                cv2.line(highlighted_img_concave, pt1, pt2, (255, 0, 0), thickness=3)
        # Plot intersection point
        intersection_point = find_intersection(polylines[0], polylines[1])
        cv2.circle(highlighted_img_concave, intersection_point, 20, (0, 255, 0), -1)

        ax.imshow(highlighted_img_concave)
        ax.set_title(item['image/name'])
        ax.axis('off')

    plt.tight_layout()
    plt.show()



def preprocess_polylines(annotations_df: pd.DataFrame):
    def parse_points_string(points_str):
        return [tuple(map(float, pt.split(','))) for pt in points_str.strip().split(';')]
    
    annotations_df['parsed_points'] = annotations_df['image/polyline/@points'].apply(parse_points_string)
    points_grouped = annotations_df.groupby('image/name')['parsed_points'].apply(list).reset_index()
    points_grouped.rename(columns={'parsed_points': 'all_polylines'}, inplace=True)
    return points_grouped

annotations_rows = parse_annotation_xml(ANNOTATIONS_PATH)
annotations_df = pd.DataFrame(annotations_rows)
# annotations_df.head()

annotations_df = preprocess_polylines(annotations_df)
annotations_df.head()

random_sampling(annotations_df, 2, 2,
                is_middle_point=True, distance_threshold=300,
                filter_x_ratio=0.03, offset_x=30)

"""
Load the image and get the first arrival peak
1. Load image
2. Hightlight first arrival peak

"""

INITIAL_IMAGE_POOL = "../data/process_data/for_label/fold_1_03052025/initial"
initial_image = os.listdir(INITIAL_IMAGE_POOL)[0]
initial_image_path = os.path.join(INITIAL_IMAGE_POOL, initial_image)
initial_image = cv2.imread(initial_image_path)
initial_image = cv2.cvtColor(initial_image, cv2.COLOR_BGR2RGB)
process_image = remove_gridlines(initial_image)
# Enlarge the size of plt
plt.figure(figsize=(10, 10))
plt.imshow(process_image)
print("Load from file name", initial_image_path)


import numpy as np
import cv2
import matplotlib.pyplot as plt

# Assuming 'process_image' is the grayscale image
three_channel_process_image = np.stack([process_image] * 3, axis=-1)

# Convert image to grayscale for peak detection (if needed)
gray_img = cv2.cvtColor(three_channel_process_image, cv2.COLOR_RGB2GRAY)

# Threshold the image to highlight peaks
_, thresholded_img = cv2.threshold(gray_img, 200, 255, cv2.THRESH_BINARY)

# Find the coordinates of the first arrival peaks (i.e., non-zero pixels in the thresholded image)
peaks = np.column_stack(np.where(thresholded_img == 255))

# Adjustable width for the highlight
highlight_width = 20  # Change this to adjust the highlight width

# Create a copy of the image to apply transparent red overlay
highlighted_img = three_channel_process_image.copy()

# We need to process row by row (trace by trace)
for row in range(thresholded_img.shape[0]):  # For each row (trace)
    # Find the peak in the current row
    row_peaks = peaks[peaks[:, 0] == row]
    
    if row_peaks.shape[0] > 0:
        # Get the middle sample for each trace (peak location in the row)
        peak_x = row_peaks[:, 1].mean()

        # Define the polygon around the peak (adjust for width)
        polygon_points = [
            (max(0, int(peak_x - highlight_width // 2)), row),
            (min(thresholded_img.shape[1], int(peak_x + highlight_width // 2)), row)
        ]

        # Draw the red polygon around the peaks (adjust color and thickness)
        cv2.fillPoly(highlighted_img, [np.array(polygon_points)], (255, 0, 0))

# Apply transparency to the red overlay (adjust opacity)
overlay = highlighted_img.copy()

# Set the red color (BGR)
red_color = np.array([255, 0, 0], dtype=np.uint8)

# Create a transparent red overlay (0.3 transparency)
alpha = 0.6
cv2.addWeighted(overlay, alpha, three_channel_process_image, 1 - alpha, 0, highlighted_img)

# Display the original image and the enhanced image with transparent red overlay
fig, axes = plt.subplots(1, 2, figsize=(18, 12))

axes[0].imshow(three_channel_process_image)
axes[0].set_title('Original Image')
axes[0].axis('off')

axes[1].imshow(highlighted_img)
axes[1].set_title('Enhanced Image with Transparent Overlay')
axes[1].axis('off')

plt.show()


import numpy as np
import cv2
import matplotlib.pyplot as plt
from scipy.spatial import ConvexHull
from scipy.spatial.distance import cdist

# Assuming 'process_image' is the grayscale image
three_channel_process_image = np.stack([process_image] * 3, axis=-1)

# Convert image to grayscale for peak detection (if needed)
gray_img = cv2.cvtColor(three_channel_process_image, cv2.COLOR_RGB2GRAY)

# Threshold the image to highlight peaks
_, thresholded_img = cv2.threshold(gray_img, 200, 255, cv2.THRESH_BINARY)

# Find the coordinates of the first arrival peaks (i.e., non-zero pixels in the thresholded image)
peaks = np.column_stack(np.where(thresholded_img == 255))

# Calculate the middle sample for each row (mean of x-coordinates for the peaks in each row)
middle_samples = []
for row in range(thresholded_img.shape[0]):  # For each row (trace)
    row_peaks = peaks[peaks[:, 0] == row]  # Get all peaks for the current row
    if row_peaks.shape[0] > 0:
        peak_x = row_peaks[:, 1].mean()  # Calculate the middle sample (mean of x-coordinates)
        middle_samples.append((peak_x, row))  # Store the middle sample with the row index

# Convert middle samples to a numpy array
middle_samples = np.array(middle_samples)

# Filter the middle samples based on distance threshold (ignore isolated points)
filtered_middle_samples = [middle_samples[0]]  # Always keep the first one

# Set the distance threshold for filtering (you can adjust this threshold)
distance_threshold = 1000

for i in range(1, len(middle_samples)):
    prev_sample = filtered_middle_samples[-1]
    curr_sample = middle_samples[i]

    # Calculate the distance between the current sample and the previous one
    distance = np.linalg.norm(curr_sample - prev_sample)

    if distance < distance_threshold:  # Keep the sample if it's within the threshold distance
        filtered_middle_samples.append(curr_sample)

# Convert filtered middle samples back to a numpy array
filtered_middle_samples = np.array(filtered_middle_samples)

# Create a copy of the image to apply transparent red overlay
highlighted_img = three_channel_process_image.copy()

# Step 1: Calculate Convex Hull for the filtered middle samples
convex_hull = cv2.convexHull(filtered_middle_samples.astype(np.int32))

# Draw the convex hull on the image
cv2.fillPoly(highlighted_img, [convex_hull], (255, 0, 0))

# Step 2: Apply transparency to the red overlay (adjust opacity)
overlay = highlighted_img.copy()

# Set the red color (BGR)
red_color = np.array([255, 0, 0], dtype=np.uint8)

# Create a transparent red overlay (adjust the opacity here by changing alpha)
alpha = 0.3  # Lower value for more transparency (e.g., 0.1 for a subtle overlay)
cv2.addWeighted(overlay, alpha, three_channel_process_image, 1 - alpha, 0, highlighted_img)

# Step 3: Calculate Concave Hull (using custom approach for demonstration)
# Here, we'll use alpha shapes for concave hull (for simplicity, we use convex hull as placeholder)
# concave_hull = convex_hull  # Placeholder for concave hull

# Draw the concave hull on the image
# cv2.fillPoly(highlighted_img, [concave_hull], (0, 255, 0))  # Green concave hull (for demonstration)

# Display the original image and the enhanced image with convex and concave hull overlay
fig, axes = plt.subplots(1, 2, figsize=(12, 6))

axes[0].imshow(three_channel_process_image)
axes[0].set_title('Original Image')
axes[0].axis('off')

axes[1].imshow(highlighted_img)
axes[1].set_title('Enhanced Image with Convex and Concave Hull Overlay')
axes[1].axis('off')

plt.show()


import numpy as np
import cv2
import matplotlib.pyplot as plt
from concave_hull import concave_hull  # Correct import for concave hull
from scipy.spatial import Delaunay
from scipy.spatial.distance import cdist

def threshold_image(process_image):
    """Threshold the image to highlight peaks and return the thresholded image."""
    three_channel_process_image = np.stack([process_image] * 3, axis=-1)
    gray_img = cv2.cvtColor(three_channel_process_image, cv2.COLOR_RGB2GRAY)
    _, thresholded_img = cv2.threshold(gray_img, 200, 255, cv2.THRESH_BINARY)
    return thresholded_img, three_channel_process_image

def get_all_peaks(thresholded_img):
    """Get all peak points from the thresholded image."""
    peaks = np.column_stack(np.where(thresholded_img == 255))  # Get all peaks (non-zero pixels)
    peaks = peaks[:, [1, 0]]
    return peaks

def calculate_middle_samples(thresholded_img, peaks):
    """Calculate the middle sample (peak) for each row (trace) in the image."""
    middle_samples = []
    for row in range(thresholded_img.shape[0]):  # For each row (trace)
        row_peaks = peaks[peaks[:, 1] == row]  # Get all peaks for the current row (filter by y-value)
        if row_peaks.shape[0] > 0:
            peak_x = row_peaks[:, 0].mean()  # Calculate the middle sample (mean of x-coordinates)
            middle_samples.append((peak_x, row))  # Store the middle sample with the row index
    return np.array(middle_samples)


def filter_peaks_by_distance(peaks, distance_threshold=50):
    """Filter peaks based on a distance threshold to exclude outliers or points too far apart."""
    filtered_peaks = [peaks[0]]  # Always keep the first peak
    for i in range(1, len(peaks)):
        prev_peak = filtered_peaks[-1]
        curr_peak = peaks[i]
        # Calculate the Euclidean distance between the current peak and the previous one
        distance = np.linalg.norm(curr_peak - prev_peak)
        
        if distance < distance_threshold:  # Keep the sample if it's within the threshold distance
            filtered_peaks.append(curr_peak)

    return np.array(filtered_peaks)

def calculate_convex_hull(peaks, three_channel_process_image):
    """Calculate the convex hull for the filtered points and return the highlighted image."""
    convex_hull = cv2.convexHull(peaks.astype(np.int32))
    highlighted_img_convex = three_channel_process_image.copy()
    cv2.fillPoly(highlighted_img_convex, [convex_hull], (255, 0, 0))
    
    # Apply transparency to the red overlay
    alpha = 0.3  # Lower value for more transparency
    overlay_convex = highlighted_img_convex.copy()
    cv2.addWeighted(overlay_convex, alpha, three_channel_process_image, 1 - alpha, 0, highlighted_img_convex)
    
    return highlighted_img_convex

def calculate_concave_hull(peaks, three_channel_process_image, concavity_alpha=4.0):
    """Calculate the concave hull using the ConcaveHull library and return the highlighted image."""
    # Apply ConcaveHull to the full set of points (peaks)
    concave_hull_points = concave_hull(peaks, concavity=concavity_alpha)
    
    # Create a copy of the image to apply concave hull
    highlighted_img_concave = three_channel_process_image.copy()
    
    # Draw the concave hull on the image (convert to integer coordinates)
    if len(concave_hull_points) > 0:
        concave_hull_coords = np.array(concave_hull_points, dtype=np.int32)
        cv2.fillPoly(highlighted_img_concave, [concave_hull_coords], (0, 255, 0))  # Green concave hull
    
    # Apply transparency to the concave hull overlay
    alpha = 0.3  # Lower value for more transparency
    overlay_concave = highlighted_img_concave.copy()
    cv2.addWeighted(overlay_concave, alpha, three_channel_process_image, 1 - alpha, 0, highlighted_img_concave)
    
    return highlighted_img_concave


def plot_peaks_on_image(original_img, peaks):
    """Plot the detected peaks (samples) as red points on the original image."""
    img_with_peaks = original_img.copy()
    for peak in peaks:
        x, y = peak
        # Ensure that x and y are integers before passing them to cv2.circle
        x, y = int(x), int(y)
        # Draw a red circle at each peak location
        cv2.circle(img_with_peaks, (x, y), 5, (255, 0, 0), -1)
    return img_with_peaks

def display_images(original_img, highlighted_img_convex, highlighted_img_concave, peaks):
    """Display the original image with peaks, convex hull overlay, and concave hull overlay."""
    # Plot the original image with the detected peaks
    img_with_peaks = plot_peaks_on_image(original_img, peaks)
    
    # Display original image with peaks and convex hull
    fig, axes = plt.subplots(1, 2, figsize=(12, 6))

    axes[0].imshow(img_with_peaks)
    axes[0].set_title('Original Image with Peaks')
    axes[0].axis('off')

    axes[1].imshow(highlighted_img_concave)
    axes[1].set_title('Enhanced Image with Concave Hull Overlay')
    axes[1].axis('off')

    plt.show()

    # Display the image with convex hull
    fig, axes = plt.subplots(1, 2, figsize=(12, 6))

    axes[0].imshow(img_with_peaks)
    axes[0].set_title('Original Image with Peaks')
    axes[0].axis('off')

    axes[1].imshow(highlighted_img_convex)
    axes[1].set_title('Enhanced Image with Convex Hull Overlay')
    axes[1].axis('off')

    plt.show()

def check_peaks_distance(peaks, distance_threshold=50):
    """Check if any peaks are too far apart and print the results."""
    far_peaks = []
    for i in range(1, len(peaks)):
        prev_peak = peaks[i-1]
        curr_peak = peaks[i]
        # Calculate the Euclidean distance between the current peak and the previous one
        distance = np.linalg.norm(curr_peak - prev_peak)
        
        if distance > distance_threshold:  # If distance exceeds threshold, record the peaks
            far_peaks.append((prev_peak, curr_peak))
    
    # Print the far-apart peaks
    if far_peaks:
        print(f"Found {len(far_peaks)} peaks that are too far apart (distance > {distance_threshold}):")
        for pair in far_peaks:
            print(f"Peak 1: {pair[0]}, Peak 2: {pair[1]}")
    else:
        print(f"No peaks found that are far apart (distance > {distance_threshold}).")

# Main function
def process_image_with_hulls(process_image, is_middle_point=False, distance_threshold=50):
    thresholded_img, three_channel_process_image = threshold_image(process_image)

    peaks = get_all_peaks(thresholded_img)
    if is_middle_point:
        middle_samples = calculate_middle_samples(thresholded_img, peaks)
        peaks = middle_samples  # Use the middle samples as the new set of peaks
    
    # check_peaks_distance(peaks, distance_threshold)
    filtered_peaks = filter_peaks_by_distance(peaks, distance_threshold)
    highlighted_img_convex = calculate_convex_hull(filtered_peaks, three_channel_process_image)
    highlighted_img_concave = calculate_concave_hull(filtered_peaks, three_channel_process_image)
    display_images(
        three_channel_process_image, highlighted_img_convex, highlighted_img_concave,
        filtered_peaks)

    return highlighted_img_concave

# Call the main function with your image and adjustable distance threshold
DISTANCE_THRESHOLD=200
h_concave = process_image_with_hulls(process_image, is_middle_point=False, distance_threshold=DISTANCE_THRESHOLD)


import numpy as np
import matplotlib.pyplot as plt


# Separate x and y coordinates
x_coords = g_peaks[:, 0]
y_coords = g_peaks[:, 1]

# Plotting the frequency (distribution) of x and y coordinates
fig, axes = plt.subplots(1, 2, figsize=(12, 6))

# Plot histogram for x coordinates
axes[0].hist(x_coords, bins=30, color='r', edgecolor='black', alpha=0.7)
axes[0].set_title("Distribution of X Coordinates")
axes[0].set_xlabel("X Value")
axes[0].set_ylabel("Frequency")
axes[0].grid(True)

# Plot histogram for y coordinates
axes[1].hist(y_coords, bins=30, color='b', edgecolor='black', alpha=0.7)
axes[1].set_title("Distribution of Y Coordinates")
axes[1].set_xlabel("Y Value")
axes[1].set_ylabel("Frequency")
axes[1].grid(True)

plt.tight_layout()
plt.show()


h_concave.shape

plt.imshow(process_image)

import numpy as np
import cv2
import matplotlib.pyplot as plt
from concave_hull import concave_hull  # Correct import for concave hull
from scipy.spatial import Delaunay
from scipy.spatial.distance import cdist

def threshold_image(process_image):
    """Threshold the image to highlight peaks and return the thresholded image."""
    three_channel_process_image = np.stack([process_image] * 3, axis=-1)
    gray_img = cv2.cvtColor(three_channel_process_image, cv2.COLOR_RGB2GRAY)
    _, thresholded_img = cv2.threshold(gray_img, 200, 255, cv2.THRESH_BINARY)
    return thresholded_img, three_channel_process_image

def get_all_peaks(thresholded_img):
    """Get all peak points from the thresholded image."""
    peaks = np.column_stack(np.where(thresholded_img == 255))  # Get all peaks (non-zero pixels)
    peaks = peaks[:, [1, 0]]  # Convert from (y, x) to (x, y) format
    return peaks

def filter_peaks_by_x_coordinate(peaks, width, threshold_ratio=0.1):
    """Remove peaks located before a specified percentage of the image width."""
    threshold_x = int(threshold_ratio * width)  # Calculate the threshold based on the image width
    filtered_peaks = peaks[peaks[:, 0] >= threshold_x]  # Keep only peaks with x >= threshold_x
    return filtered_peaks

def calculate_middle_samples(thresholded_img, peaks, offset_x=10):
    """Calculate the middle sample (peak) for each row (trace) and generate new peaks."""
    middle_samples = []
    for row in range(thresholded_img.shape[0]):  # For each row (trace)
        row_peaks = peaks[peaks[:, 1] == row]  # Get all peaks for the current row (filter by y-value)
        if row_peaks.shape[0] > 0:
            peak_x = row_peaks[:, 0].mean()  # Calculate the middle sample (mean of x-coordinates)
            
            # Generate two new peaks based on the middle point
            new_peaks = [
                (peak_x - offset_x, row),  # Peak at x - offset_x
                (peak_x + offset_x, row)   # Peak at x + offset_x
            ]
            middle_samples.extend(new_peaks)  # Add the new peaks to the list

    return np.array(middle_samples)

def filter_peaks_by_distance(peaks, distance_threshold=50):
    """Filter peaks based on a distance threshold to exclude outliers or points too far apart."""
    filtered_peaks = [peaks[0]]  # Always keep the first peak
    for i in range(1, len(peaks)):
        prev_peak = filtered_peaks[-1]
        curr_peak = peaks[i]
        # Calculate the Euclidean distance between the current peak and the previous one
        distance = np.linalg.norm(curr_peak - prev_peak)
        
        if distance < distance_threshold:  # Keep the sample if it's within the threshold distance
            filtered_peaks.append(curr_peak)

    return np.array(filtered_peaks)

def calculate_convex_hull(peaks, three_channel_process_image):
    """Calculate the convex hull for the filtered points and return the highlighted image."""
    convex_hull = cv2.convexHull(peaks.astype(np.int32))
    highlighted_img_convex = three_channel_process_image.copy()
    cv2.fillPoly(highlighted_img_convex, [convex_hull], (255, 0, 0))
    
    # Apply transparency to the red overlay
    alpha = 0.3  # Lower value for more transparency
    overlay_convex = highlighted_img_convex.copy()
    cv2.addWeighted(overlay_convex, alpha, three_channel_process_image, 1 - alpha, 0, highlighted_img_convex)
    
    return highlighted_img_convex

def calculate_concave_hull(peaks, three_channel_process_image, concavity_alpha=4.0):
    """Calculate the concave hull using the ConcaveHull library and return the highlighted image."""
    # Apply ConcaveHull to the full set of points (peaks)
    concave_hull_points = concave_hull(peaks, concavity=concavity_alpha)
    
    # Create a copy of the image to apply concave hull
    highlighted_img_concave = three_channel_process_image.copy()
    
    # Draw the concave hull on the image (convert to integer coordinates)
    if len(concave_hull_points) > 0:
        concave_hull_coords = np.array(concave_hull_points, dtype=np.int32)
        cv2.fillPoly(highlighted_img_concave, [concave_hull_coords], (0, 255, 0))  # Green concave hull
    
    # Apply transparency to the concave hull overlay
    alpha = 0.3  # Lower value for more transparency
    overlay_concave = highlighted_img_concave.copy()
    cv2.addWeighted(overlay_concave, alpha, three_channel_process_image, 1 - alpha, 0, highlighted_img_concave)
    
    return highlighted_img_concave

def plot_peaks_on_image(original_img, peaks):
    """Plot the detected peaks (samples) as red points on the original image."""
    img_with_peaks = original_img.copy()
    for peak in peaks:
        x, y = peak
        # Ensure that x and y are integers before passing them to cv2.circle
        x, y = int(x), int(y)
        # Draw a red circle at each peak location
        cv2.circle(img_with_peaks, (x, y), 5, (255, 0, 0), -1)
    return img_with_peaks

def display_images(original_img, highlighted_img_convex, highlighted_img_concave, peaks, convex_hull_points):
    """Display the original image with peaks, convex hull overlay, and concave hull overlay."""
    # Plot the original image with the detected peaks
    img_with_peaks = plot_peaks_on_image(original_img, peaks)
    
    # Display original image with peaks and convex hull
    fig, axes = plt.subplots(1, 2, figsize=(12, 6))

    axes[0].imshow(img_with_peaks)
    axes[0].set_title('Original Image with Peaks')
    axes[0].axis('off')

    axes[1].imshow(highlighted_img_concave)
    axes[1].set_title('Enhanced Image with Concave Hull Overlay')
    axes[1].axis('off')

    plt.show()

    # Display the image with convex hull
    fig, axes = plt.subplots(1, 2, figsize=(12, 6))

    axes[0].imshow(img_with_peaks)
    axes[0].set_title('Original Image with Peaks')
    axes[0].axis('off')

    axes[1].imshow(highlighted_img_convex)
    axes[1].set_title('Enhanced Image with Convex Hull Overlay')
    axes[1].axis('off')

    plt.show()

def check_peaks_distance(peaks, distance_threshold=50):
    """Check if any peaks are too far apart and print the results."""
    far_peaks = []
    for i in range(1, len(peaks)):
        prev_peak = peaks[i-1]
        curr_peak = peaks[i]
        # Calculate the Euclidean distance between the current peak and the previous one
        distance = np.linalg.norm(curr_peak - prev_peak)
        
        if distance > distance_threshold:  # If distance exceeds threshold, record the peaks
            far_peaks.append((prev_peak, curr_peak))
    
    # Print the far-apart peaks
    if far_peaks:
        print(f"Found {len(far_peaks)} peaks that are too far apart (distance > {distance_threshold}):")
        for pair in far_peaks:
            print(f"Peak 1: {pair[0]}, Peak 2: {pair[1]}")
    else:
        print(f"No peaks found that are far apart (distance > {distance_threshold}).")

# Main function
def process_image_with_hulls(process_image, is_middle_point=False, distance_threshold=50, offset_x=10):
    thresholded_img, three_channel_process_image = threshold_image(process_image)

    peaks = get_all_peaks(thresholded_img)

    # Filter out peaks with x < 10% of the width
    width = process_image.shape[1]
    filtered_peaks = filter_peaks_by_x_coordinate(peaks, width, threshold_ratio=0.05)
    
    # If middle point calculation is enabled, recalculate the middle samples
    if is_middle_point:
        middle_samples = calculate_middle_samples(thresholded_img, filtered_peaks, offset_x=offset_x)
        filtered_peaks = middle_samples  # Use the middle samples as the new set of peaks
    
    # check_peaks_distance(filtered_peaks, distance_threshold)
    filtered_peaks = filter_peaks_by_distance(filtered_peaks, distance_threshold)
    
    # Calculate Convex Hull
    highlighted_img_convex = calculate_convex_hull(filtered_peaks, three_channel_process_image)
    
    # Calculate Concave Hull
    highlighted_img_concave = calculate_concave_hull(filtered_peaks, three_channel_process_image)
    
    # Display the results
    display_images(three_channel_process_image, highlighted_img_convex, highlighted_img_concave, filtered_peaks, filtered_peaks)

    return filtered_peaks

# Call the main function with your image and adjustable distance threshold
process_image_with_hulls(process_image, is_middle_point=True, distance_threshold=300, offset_x=50)




import os
from glob import glob

IMAGE_ROOT = "../data/process_data/for_label/"
INIT_POOL = "../data/process_data/for_label/init_pool"
sub_folder = ['full', 'initial']

import os
import re
from natsort import natsorted

IMAGE_ROOT = "../data/process_data/for_label"  # Adjust this path as needed
target_folds = ["fold_1_03052025", "fold_2_03052025", "fold_3_03052025", "fold_4_03052025"]
target_subdirs = ["full", "initial"]
jpg_files = {}  # Dictionary to store files by fold

for fold in target_folds:
    jpg_files[fold] = {"full": [], "initial": []}

for root, dirs, files in os.walk(IMAGE_ROOT):
    current_dir = os.path.basename(root)
    parent_dir = os.path.basename(os.path.dirname(root))
    if current_dir in target_folds:
        dirs[:] = [d for d in dirs if d in target_subdirs]
    if parent_dir in target_folds and current_dir in target_subdirs:
        for file in files:
            if file.lower().endswith('.jpg'):
                file_path = os.path.join(root, file)
                jpg_files[parent_dir][current_dir].append(file_path)
                # print(f"Found JPG in {parent_dir}/{current_dir}: {file}")

# Print summary
for fold in target_folds:
    for subdir in target_subdirs:
        count = len(jpg_files[fold][subdir])
        # print(f"{fold}/{subdir}: {count} JPG files")


# INIT POOL
init_files = glob(os.path.join(INIT_POOL, "*.jpg"))



import xml.etree.ElementTree as ET
import pandas as pd
from typing import Tuple, Optional

def parse_annotation_xml(xml_path):
    tree = ET.parse(xml_path)
    root = tree.getroot()

    rows = []

    for image in root.findall("image"):
        image_id = image.attrib.get("id")
        image_name = image.attrib.get("name")
        image_width = image.attrib.get("width")
        image_height = image.attrib.get("height")

        for poly in image.findall("polyline"):
            row = {
                "image/id": image_id,
                "image/name": image_name,
                "image/width": image_width,
                "image/height": image_height,
                "image/polyline/@label": poly.attrib.get("label"),
                "image/polyline/@points": poly.attrib.get("points"),
            }
            rows.append(row)

    return rows

def preprocess_polylines(annotations_df: pd.DataFrame):
    def parse_points_string(points_str):
        return [tuple(map(float, pt.split(','))) for pt in points_str.strip().split(';')]
    
    annotations_df['parsed_points'] = annotations_df['image/polyline/@points'].apply(parse_points_string)
    points_grouped = annotations_df.groupby('image/name')['parsed_points'].apply(list).reset_index()
    points_grouped.rename(columns={'parsed_points': 'all_polylines'}, inplace=True)
    return points_grouped


import pandas as pd

XML_FILES = [
    "../data/process_data/for_label/fold_1_03052025/annotations.xml",
    "../data/process_data/for_label/fold_2_03052025/annotations.xml",
    "../data/process_data/for_label/fold_3_03052025/annotations.xml",
    "../data/process_data/for_label/fold_4_03052025/annotations.xml"
]

# Container to hold each fold's dataframe
all_dfs = []

# READ EACH FILE AND ASSEMBLE PANDAS
for xml_file in XML_FILES:
    annotations_rows = parse_annotation_xml(xml_file)
    df = pd.DataFrame(annotations_rows)
    df = preprocess_polylines(df)
    print(f"{xml_file} --> {len(df)} rows")
    all_dfs.append(df)

# Concatenate all DataFrames into one
annotations_df = pd.concat(all_dfs, ignore_index=True)


# from image_display_utils import show_full_and_initial_images
# show_full_and_initial_images(
#     jpg_files, 
#     init_files,
#     fold_name="fold_1_03052025", num_pairs=10,
#     label_df=annotations_df)

annotations_df.head()

"""
FINAL STEP TO PREPARE Keypoints detection for training YOLO
1. Assemble all file for each fold folder
2. Prepare initial image list
3. prepare annotations_df
4. Walkthrough each annotations_df, get full image and initial image
5. Process remove gridlines, 
concave hull image analysis plot to full image and save image as new folder images/train
6. Written ultralytics YOLO keypoints labeled to .txt file
7. Save to folder name label/train

Optional flag : negative_sample_prepare
It still be left image that has no label information base on the dataframe 
We will process the left image by
- Remove gridlines
- Save to images/train as negative sample
- Create empty .txt file for the negative sample

"""

from image_display_utils import prepare_yolo_keypoint_training_data

output_base_dir = "../data/process_data/for_training"

stats = prepare_yolo_keypoint_training_data(
    jpg_files=jpg_files,
    init_files=init_files,
    label_df=annotations_df,
    output_base_dir=output_base_dir,
    negative_sample_prepare=True,
    number_of_negative_sampling=50
)

from image_display_utils import prepare_yolo_segmentation_training_data
output_base_dir = "../data/process_data/for_training"

seg_stats = prepare_yolo_segmentation_training_data(
    jpg_files=jpg_files,
    init_files=init_files,
    label_df=annotations_df,
    output_base_dir=output_base_dir,
    negative_sample_prepare=True,
    number_of_negative_sampling=50
)

# WRITE ME THE CODE TO READ FILENAME FROM IMAGES/TRAIN and write the file name to
# file on data/process_data/for_training/train.txt
seg_stats

from image_display_utils import visualize_yolo_segmentation_data, fix_yolo_segmentation_coordinates

visualize_yolo_segmentation_data(
    images_dir="../data/process_data/for_training/images/validation",
    labels_dir="../data/process_data/for_training/labels/validation",
    n_rows=2, n_cols=3)

# 2. Fix coordinate alignment (swap x,y)
train_fix_stats = fix_yolo_segmentation_coordinates(
    '../data/process_data/for_training/labels/train',
    backup=True
)

val_fix_stats = fix_yolo_segmentation_coordinates(
    '../data/process_data/for_training/labels/validation',
    backup=True
)

from image_display_utils import simplify_yolo_segmentation_masks

train_simplify_states = simplify_yolo_segmentation_masks(
    '../data/process_data/for_training/labels/train',
    backup=True
)

val_simplify_stats = simplify_yolo_segmentation_masks(
    '../data/process_data/for_training/labels/validation',
    backup=True
)

