import os
import re

IMAGE_ROOT = "../data/process_data/for_label"  # Adjust this path as needed
target_folds = ["fold_1_03052025", "fold_2_03052025", "fold_3_03052025", "fold_4_03052025"]
target_subdirs = ["full", "initial"]
jpg_files = {}  # Dictionary to store files by fold

# Initialize the dictionary with empty lists for each fold
for fold in target_folds:
    jpg_files[fold] = {"full": [], "initial": []}

for root, dirs, files in os.walk(IMAGE_ROOT):
    # Get the current directory name
    current_dir = os.path.basename(root)
    parent_dir = os.path.basename(os.path.dirname(root))
    
    # Check if we're in one of the target fold directories
    if current_dir in target_folds:
        # Filter dirs to only include "full" and "initial"
        dirs[:] = [d for d in dirs if d in target_subdirs]
    
    # Check if we're in a "full" or "initial" subdirectory of a target fold
    if parent_dir in target_folds and current_dir in target_subdirs:
        # Get all jpg files
        for file in files:
            if file.lower().endswith('.jpg'):
                file_path = os.path.join(root, file)
                jpg_files[parent_dir][current_dir].append(file_path)
                print(f"Found JPG in {parent_dir}/{current_dir}: {file}")

# Print summary
for fold in target_folds:
    for subdir in target_subdirs:
        count = len(jpg_files[fold][subdir])
        print(f"{fold}/{subdir}: {count} JPG files")