"""
Module to prepare "Parallel seismic" and "Sonic logging" data for labelling
"""

import os
import glob
from pdf2image import convert_from_path
from tqdm import tqdm

class DataPreparer:
    def __init__(self, raw_data_dir: str):
        self.raw_data_dir = raw_data_dir
        self.output_dir = "data/process_data/for_label"

    def ensure_directory_exists(self, directory: str) -> None:
        """
        Ensure that the specified directory exists, creating it if necessary.

        Args:
            directory (str): Directory path to check/create
        """
        if not os.path.exists(directory):
            os.makedirs(directory, exist_ok=True)
            print(f"Created directory: {directory}")

    def crop_image(self, image, crop_coords=None):
        """
        Crop an image based on relative coordinates.

        Args:
            image: PIL Image object
            crop_coords (List[Tuple[float, float]], optional): List of two tuples representing
                the top-left and bottom-right coordinates as relative values (0.0 to 1.0).
                Default is None, which returns the original image.

        Returns:
            PIL Image: Cropped image
        """
        if crop_coords is None:
            return image

        # Get image dimensions
        width, height = image.size

        # Calculate absolute coordinates from relative coordinates
        x1, y1 = crop_coords[0]
        x2, y2 = crop_coords[1]

        # Convert relative coordinates to absolute pixel values
        left = int(x1 * width)
        top = int(y1 * height)
        right = int(x2 * width)
        bottom = int(y2 * height)

        # Crop the image
        cropped_image = image.crop((left, top, right, bottom))
        return cropped_image

    def convert_pdf_to_images(self, pdf_path: str, output_dir: str, dpi: int = 300,
                             output_format: str = "jpg", crop_images: bool = False,
                             crop_coords=None):
        """
        Convert a PDF file to images and save them to the specified directory.

        Args:
            pdf_path (str): Path to the PDF file
            output_dir (str): Directory to save the converted images
            dpi (int, optional): DPI for the output images. Defaults to 300.
            output_format (str, optional): Format to save the images as. Defaults to "jpg".
            crop_images (bool, optional): Whether to crop the images. Defaults to False.
            crop_coords (List[Tuple[float, float]], optional): List of two tuples representing
                the top-left and bottom-right coordinates as relative values (0.0 to 1.0).
        """
        try:
            # Get the base filename without extension
            pdf_filename = os.path.basename(pdf_path)
            pdf_name = os.path.splitext(pdf_filename)[0]

            # Convert PDF to images
            images = convert_from_path(pdf_path, dpi=dpi)

            # Process each image
            for i, image in enumerate(images):
                # Apply cropping if requested
                if crop_images and crop_coords:
                    processed_image = self.crop_image(image, crop_coords)
                else:
                    processed_image = image

                # Create filename for the image
                image_filename = f"{pdf_name}_page_{i+1}.{output_format}"
                image_path = os.path.join(output_dir, image_filename)

                # Save the image
                processed_image.save(image_path, "JPEG")

        except Exception as e:
            print(f"Error processing '{pdf_path}': {str(e)}")

    # MAIN METHOD
    def prepare_raw_data(self, n_fold=4, crop_images=False, crop_coords=None):
        """
        Process PDF files and convert them to images, distributing them across fold folders.

        Steps:
        1. Specified data to process, get pdf sample and filter only non "initial" in file name
        2. Call function to convert and crop
        3. Save image to specified folder ( seperate by fold folder (n input arguments))
        ( Optional ) Processed initial -> Save to the same structure as previous but in the sub folder "initial"

        Args:
            n_fold (int, optional): Number of fold folders to create. Defaults to 4.
            crop_images (bool, optional): Whether to crop the images. Defaults to False.
            crop_coords (List[Tuple[float, float]], optional): List of two tuples representing
                the top-left and bottom-right coordinates as relative values (0.0 to 1.0).
                Example: [(0.1, 0.1), (0.9, 0.9)] for cropping 10% from each edge.

        Returns:
            str: Path to the output directory
        """
        # Get all PDF files from the raw data directory
        pdf_files = glob.glob(os.path.join(self.raw_data_dir, "*.pdf"))

        if not pdf_files:
            raise FileNotFoundError(f"No PDF files found in '{self.raw_data_dir}'.")

        # Filter files into two groups: "initial" and "full" (non-initial)
        full_pdf_files = [pdf for pdf in pdf_files if "initial" not in os.path.basename(pdf).lower()]
        initial_pdf_files = [pdf for pdf in pdf_files if "initial" in os.path.basename(pdf).lower()]

        print(f"Found {len(full_pdf_files)} full PDF files and {len(initial_pdf_files)} initial PDF files")

        # Create output directory structure with fold folders
        for fold in range(1, n_fold + 1):
            # Create fold directory
            fold_dir = os.path.join(self.output_dir, f"fold_{fold}")
            self.ensure_directory_exists(fold_dir)

            # Create full and initial subdirectories
            full_dir = os.path.join(fold_dir, "full")
            initial_dir = os.path.join(fold_dir, "initial")
            self.ensure_directory_exists(full_dir)
            self.ensure_directory_exists(initial_dir)

        # Distribute PDFs across folds
        for i, pdf_path in enumerate(tqdm(full_pdf_files, desc="Processing full PDFs")):
            # Determine which fold to put this PDF in
            fold_index = (i % n_fold) + 1
            output_dir = os.path.join(self.output_dir, f"fold_{fold_index}", "full")

            # Convert PDF to images and save them
            self.convert_pdf_to_images(
                pdf_path,
                output_dir,
                crop_images=crop_images,
                crop_coords=crop_coords
            )

        # Process initial PDFs if available
        for i, pdf_path in enumerate(tqdm(initial_pdf_files, desc="Processing initial PDFs")):
            # Determine which fold to put this PDF in
            fold_index = (i % n_fold) + 1
            output_dir = os.path.join(self.output_dir, f"fold_{fold_index}", "initial")

            # Convert PDF to images and save them
            self.convert_pdf_to_images(
                pdf_path,
                output_dir,
                crop_images=crop_images,
                crop_coords=crop_coords
            )

        print(f"PDF processing complete. Output saved to {self.output_dir}")
        return self.output_dir


# Example usage
if __name__ == "__main__":
    # Example: Process PDFs from a directory
    raw_data_dir = "data/Parallel_pdf_07042568"  # Replace with your PDF directory
    preparer = DataPreparer(raw_data_dir)

    # # Example 1: Basic usage - no cropping
    # preparer.prepare_raw_data(n_fold=4)

    # # Example 2: With cropping - crop 10% from each edge
    # crop_coords = [(0.10, 0.10), (0.90, 0.90)]
    # preparer.prepare_raw_data(n_fold=4, crop_images=True, crop_coords=crop_coords)

    # # Example 3: With custom cropping coordinates
    custom_crop_coords = [(0.10, 0.25), (0.87, 0.77)]
    preparer.prepare_raw_data(n_fold=4, crop_images=True, crop_coords=custom_crop_coords)

    print("To use this module, create an instance of DataPreparer with your PDF directory:")
    print("preparer = DataPreparer('path/to/pdf/directory')")
    print("Then call prepare_raw_data() with your desired parameters.")