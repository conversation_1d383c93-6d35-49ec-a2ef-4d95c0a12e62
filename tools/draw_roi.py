import cv2

def draw_roi(image_path):
    roi_coords = []
    img = cv2.imread(image_path)
    if img is None:
        raise FileNotFoundError(f"Could not load image at: {image_path}")
    
    clone = img.copy()

    def mouse_callback(event, x, y, flags, param):
        nonlocal img
        if event == cv2.EVENT_LBUTTONDOWN:
            roi_coords.clear()
            roi_coords.append((x, y))
        elif event == cv2.EVENT_LBUTTONUP:
            roi_coords.append((x, y))
            cv2.rectangle(img, roi_coords[0], roi_coords[1], (0, 255, 0), 5)
            cv2.imshow("ROI Selector", img)
            print(f"Top-left: {roi_coords[0]}, Bottom-right: {roi_coords[1]}")

    cv2.namedWindow("ROI Selector")
    cv2.setMouseCallback("ROI Selector", mouse_callback)

    while True:
        cv2.imshow("ROI Selector", img)
        key = cv2.waitKey(1) & 0xFF
        if key == ord("r"):  # Reset drawing
            img = clone.copy()
            roi_coords.clear()
        elif key == ord("q"):  # Quit
            break

    cv2.destroyAllWindows()

    if len(roi_coords) == 2:
        return roi_coords[0], roi_coords[1]
    else:
        return None

# Example usage
if __name__ == "__main__":
    image_path = "output/full_images/Huahin.1_page_1.jpg"  # Set your image path here
    coords = draw_roi(image_path)
    if coords:
        print("ROI Coordinates:", coords)
    else:
        print("No ROI was selected.")
